

/**
 * @file epd_display_new.c
 * @brief 新屏幕EPD显示驱动实现
 * @details 基于370bw_code.c和3.7局刷wf_v1.txt的新屏幕驱动
 * <AUTHOR> Team
 * @date 2024
 */

#include "epd_display.h"
#include "epd_display_new.h"
#include "epd_spi.h"
#include "epd_config.h"
#include "epd_predefined_data.h"
#include "epd_partial_s37t03.h"
#include "../hardware/hardware_config.h"
#include "system/includes.h"
#include "gpio.h"

// 刷新模式选择：0=局部刷新(DU), 1=全刷新(GC)
#define EPD_REFRESH_MODE_DU     0
#define EPD_REFRESH_MODE_GC     1

// 当前刷新模式，默认使用局部刷新
static int g_epd_refresh_mode = EPD_REFRESH_MODE_DU;

/**
 * @brief UC8253c窗口坐标结构体
 */
typedef struct {
    unsigned char hrst;  // 水平起始通道组 [7:3]
    unsigned char hred;  // 水平结束通道组 [7:3]
    unsigned int vrst;   // 垂直起始扫描线 [8:0]
    unsigned int vred;   // 垂直结束扫描线 [8:0]
} uc8253c_window_t;

// EPD电源状态管理
static epd_power_state_t g_epd_power_state = EPD_POWER_STATE_UNINITIALIZED;

// 静态函数前向声明
static epd_status_t epd_check_busy(void);
static void epd_write_lut_tables(void);
static epd_status_t epd_verify_partial_mode_uc8253c(void);
static epd_status_t epd_configure_psr_for_register_lut(void);
static epd_status_t epd_convert_coordinates_to_uc8253c(epd_refresh_area_t area, uc8253c_window_t* window);
static epd_status_t epd_test_simple_partial_window(unsigned char hrst, unsigned char hred, unsigned int vrst, unsigned int vred);
static epd_status_t epd_partial_refresh_uc8253c_complete(epd_refresh_area_t area, const unsigned char* new_data, unsigned int data_size);

// 新屏幕驱动上下文（统一架构）
static epd_new_context_t g_new_ctx = {0};

// 区域坐标定义（与GUI绘图坐标系统一）
static const epd_area_coord_t g_area_coords[] = {
    [EPD_AREA_QUESTION] = {0, 85, 240, 58},   // 问题区域: Y=85-143 (58像素高)
    [EPD_AREA_ANSWER]   = {0, 150, 240, 196}, // 回答区域: Y=150-345 (196像素高)
    [EPD_AREA_FULL]     = {0, 0, 240, 416}    // 全屏区域: 240x416
};

/**
 * @brief 全刷新LUT查找表 (GC模式)
 * @details 用于EPD全刷新的波形数据，控制像素的刷新时序
 *          移植自新屏幕370bw_code.c的GC模式LUT配置
 */
static const unsigned char lut_R20_GC[] = {
    0x01, 0x3c, 0x14, 0x14, 0x14, 0x01, 0x01,
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
};

static const unsigned char lut_R21_GC[] = {
    0x01, 0x7c, 0x94, 0x94, 0x94, 0x01, 0x01,
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
};

static const unsigned char lut_R22_GC[] = {
    0x01, 0x7c, 0x94, 0x94, 0x94, 0x01, 0x01,
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
};

static const unsigned char lut_R23_GC[] = {
    0x01, 0xbc, 0x54, 0x54, 0x54, 0x01, 0x01,
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
};

static const unsigned char lut_R24_GC[] = {
    0x01, 0xbc, 0x54, 0x54, 0x54, 0x01, 0x01,
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
};

/**
 * @brief 局部刷新LUT查找表 (DU模式)
 * @details 用于EPD局部刷新的波形数据，控制像素的刷新时序
 *          移植自新屏幕370bw_code.c和3.7局刷wf_v1.txt的DU模式LUT配置
 */
static const unsigned char lut_R20_DU[] = {
    0x01, 0x08, 0x01, 0x08, 0x08, 0x01, 0x01,
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
};

static const unsigned char lut_R21_DU[] = {
    0x01, 0x88, 0x01, 0x88, 0x88, 0x01, 0x01,
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
};

static const unsigned char lut_R22_DU[] = {
    0x01, 0x88, 0x01, 0x88, 0x88, 0x01, 0x01,
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
};

static const unsigned char lut_R23_DU[] = {
    0x01, 0x48, 0x01, 0x48, 0x48, 0x01, 0x01,
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
};

static const unsigned char lut_R24_DU[] = {
    0x01, 0x48, 0x01, 0x48, 0x48, 0x01, 0x01,
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
};

/**
 * @brief 简化的坐标处理（参照Demo_epd_du的像素坐标方式）
 * @param area 刷新区域枚举
 * @param x_start 输出X起始坐标
 * @param x_end 输出X结束坐标
 * @param y_start 输出Y起始坐标
 * @param y_end 输出Y结束坐标
 * @return EPD状态
 */
static epd_status_t epd_convert_coordinates_simple(epd_refresh_area_t area,
                                                  unsigned int* x_start, unsigned int* x_end,
                                                  unsigned int* y_start, unsigned int* y_end)
{
    const epd_area_coord_t* coord = epd_get_area_coord_new(area);
    if (coord == NULL || x_start == NULL || x_end == NULL || y_start == NULL || y_end == NULL) {
        printf("EPD: Invalid parameters for coordinate conversion\r\n");
        return EPD_STATUS_INVALID_PARAM;
    }

    printf("EPD: Original coordinates - x_start=%d, width=%d, y_start=%d, height=%d\r\n",
           coord->x_start, coord->width, coord->y_start, coord->height);

    // 参照Demo_epd_du的坐标处理方式：简单的像素坐标+8字节对齐
    *x_start = coord->x_start;
    *y_start = coord->y_start;  // 保持正确的Y=85，不要用Demo_epd_du的130

    // X轴8字节对齐（学习Demo_epd_du的处理方式）
    *x_start = *x_start - (*x_start % 8);
    *x_end = *x_start + coord->width - 1;
    *y_end = *y_start + coord->height - 1;

    // 边界检查
    if (*x_end >= EPD_WIDTH || *y_end >= EPD_HEIGHT) {
        printf("EPD: Coordinates out of bounds - X:%d-%d, Y:%d-%d\r\n",
               *x_start, *x_end, *y_start, *y_end);
        return EPD_STATUS_ERROR;
    }

    printf("EPD: Simple coordinates - X:%d-%d, Y:%d-%d\r\n",
           *x_start, *x_end, *y_start, *y_end);

    return EPD_STATUS_OK;
}

/**
 * @brief 配置PSR寄存器使用寄存器LUT (UC8253c)
 * @return EPD状态
 */
static epd_status_t epd_configure_psr_for_register_lut(void)
{
    printf("EPD: Configuring PSR for register LUT\r\n");

    // PSR (0x00) - Panel Setting
    epd_write_cmd(0x00);

    // 构建PSR字节：
    // D7-D6: RES[1:0] = 00 (240x416)
    // D5: REG = 1 (使用寄存器LUT)
    // D4: KW/R = 1 (KW模式，黑白)
    // D3: UD = 0 (向下扫描)
    // D2: SHL = 1 (向右移位，与GUI绘图一致)
    // D1: SHD_N = 1 (升压电路开启)
    // D0: RST_N = 1 (正常运行)
    unsigned char psr_value = 0x00 |  // RES[1:0] = 00
                              (1 << 5) |  // REG = 1 (寄存器LUT)
                              (1 << 4) |  // KW/R = 1 (KW模式)
                              (0 << 3) |  // UD = 0 (向下扫描)
                              (1 << 2) |  // SHL = 1 (向右移位，预设值)
                              (1 << 1) |  // SHD_N = 1
                              (1 << 0);   // RST_N = 1

    epd_write_data(psr_value);  // 0x36 = 0011 0110

    printf("EPD: PSR set to 0x%02X (REG=1, SHL=1 for right shift)\r\n", psr_value);
    return EPD_STATUS_OK;
}

/**
 * @brief 测试简单局部刷新窗口 (调试用)
 * @param hrst 水平起始通道组
 * @param hred 水平结束通道组
 * @param vrst 垂直起始扫描线
 * @param vred 垂直结束扫描线
 * @return EPD状态
 */
static epd_status_t epd_test_simple_partial_window(unsigned char hrst, unsigned char hred,
                                                   unsigned int vrst, unsigned int vred)
{
    printf("EPD: Testing simple partial window - HRST:0x%02X, HRED:0x%02X, VRST:%d, VRED:%d\r\n",
           hrst, hred, vrst, vred);

    // 计算窗口大小
    unsigned int window_width_pixels = (hred - hrst + 1) * 8;
    unsigned int window_height_pixels = vred - vrst + 1;
    unsigned int window_bytes = (window_width_pixels * window_height_pixels) / 8;

    printf("EPD: Test window size - %dx%d pixels, %d bytes\r\n",
           window_width_pixels, window_height_pixels, window_bytes);

    // 配置PSR
    epd_status_t ret = epd_configure_psr_for_register_lut();
    if (ret != EPD_STATUS_OK) return ret;

    // 加载LUT
    epd_write_lut_tables();

    // 电源开启
    epd_write_cmd(0x04);  // PON
    ret = epd_check_busy();
    if (ret != EPD_STATUS_OK) return ret;

    // 进入局部模式
    epd_write_cmd(0x91);  // PTIN

    // 设置测试窗口
    epd_write_cmd(0x90);  // PTL
    epd_write_data(hrst);
    epd_write_data(hred);
    epd_write_data((vrst >> 8) & 0xFF);
    epd_write_data(vrst & 0xFF);
    epd_write_data((vred >> 8) & 0xFF);
    epd_write_data(vred & 0xFF);
    epd_write_data(0x01);  // PT_SCAN

    // 验证局部模式
    epd_verify_partial_mode_uc8253c();

    // 传输测试数据 (棋盘格图案)
    printf("EPD: Transmitting test pattern data\r\n");

    // OLD数据 (全白)
    epd_write_cmd(0x10);
    for (unsigned int i = 0; i < window_bytes; i++) {
        epd_write_data(0xFF);
    }

    // NEW数据 (棋盘格)
    epd_write_cmd(0x13);
    for (unsigned int i = 0; i < window_bytes; i++) {
        epd_write_data((i % 2) ? 0xAA : 0x55);  // 棋盘格图案
    }

    // 执行刷新
    epd_write_cmd(0x12);  // DRF
    ret = epd_check_busy();
    if (ret != EPD_STATUS_OK) return ret;

    // 退出局部模式
    epd_write_cmd(0x92);  // PTOUT

    // 电源关闭
    epd_write_cmd(0x02);  // POF
    ret = epd_check_busy();
    if (ret != EPD_STATUS_OK) return ret;

    printf("EPD: Test window refresh completed\r\n");
    return EPD_STATUS_OK;
}

/**
 * @brief 验证局部模式状态 (UC8253c)
 * @return EPD状态
 */
static epd_status_t epd_verify_partial_mode_uc8253c(void)
{
    printf("EPD: Verifying partial mode status...\r\n");

    // 等待200us后才能读取状态 (根据UC8253c文档)
    epd_spi_delay_us(200);

    epd_write_cmd(0x71);  // FLG - Get Status
    unsigned char status = epd_read_data();

    printf("EPD: Status register = 0x%02X\r\n", status);
    printf("EPD: PTL_FLAG (D6) = %d\r\n", (status >> 6) & 1);
    printf("EPD: data_flag (D3) = %d\r\n", (status >> 3) & 1);
    printf("EPD: PON (D2) = %d\r\n", (status >> 2) & 1);
    printf("EPD: POF (D1) = %d\r\n", (status >> 1) & 1);
    printf("EPD: BUSY_N (D0) = %d\r\n", status & 1);

    if ((status >> 6) & 1) {
        printf("EPD: ✓ Partial mode confirmed\r\n");
        return EPD_STATUS_OK;
    } else {
        printf("EPD: ✗ Partial mode NOT set\r\n");
        return EPD_STATUS_ERROR;
    }
}

/**
 * @brief 检查EPD忙状态 (UC8253c正确实现)
 * @note BUSY_N低电平有效：0=忙碌，1=空闲
 */
#define EPD_BUSY_TIMEOUT_MS 5000  // 增加超时时间到5秒
static epd_status_t epd_check_busy(void)
{
    unsigned int timeout_counter = 0;
    const unsigned int max_timeout = EPD_BUSY_TIMEOUT_MS * 10;
    unsigned int check_interval = 1000;  // 初始1ms间隔
    const unsigned int max_interval = 10000;   // 最大10ms间隔

    printf("EPD: Checking BUSY_N status (initial=%d, 0=busy, 1=idle)...\r\n", EPD_BUSY_READ());

    // 修正：BUSY_N低电平有效，等待变为高电平(空闲)
    while (EPD_BUSY_READ() == 0) {  // 0=忙碌，等待变为1(空闲)
        epd_spi_delay_us(check_interval);  // 使用动态间隔
        timeout_counter++;

        if (timeout_counter >= max_timeout) {
            printf("EPD: BUSY timeout! Still busy after %dms\r\n", EPD_BUSY_TIMEOUT_MS);
            return EPD_STATUS_TIMEOUT;
        }

        // 逐渐增加检查间隔，减少CPU占用
        if (check_interval < max_interval) {
            check_interval += 500;  // 每次增加0.5ms
        }

        // 每秒打印一次状态，避免日志过多
        if (timeout_counter % 1000 == 0) {  // 调整为每1000次检查打印一次
            printf("EPD: Still busy, waiting... (interval=%dus, count=%d)\r\n",
                   check_interval, timeout_counter);
        }
    }

    epd_spi_delay_us(10);  // 额外延时确保稳定
    printf("EPD: BUSY_N check completed (final=%d, idle)\r\n", EPD_BUSY_READ());
    return EPD_STATUS_OK;
}

/**
 * @brief 检查EPD忙状态（公共接口）
 * @details 等待EPD完成当前操作，供外部模块调用
 * @return EPD状态
 */
epd_status_t epd_check_busy_status(void)
{
    return epd_check_busy();
}

// EPD硬件复位函数已在epd_spi.c中实现，这里不需要重复定义

/**
 * @brief 设置EPD刷新模式
 * @param mode 刷新模式：EPD_REFRESH_MODE_DU(局部) 或 EPD_REFRESH_MODE_GC(全刷新)
 */
void epd_set_refresh_mode(int mode)
{
    g_epd_refresh_mode = mode;
    printf("EPD refresh mode set to: %s\r\n", 
           mode == EPD_REFRESH_MODE_GC ? "GC (Full)" : "DU (Partial)");
}

/**
 * @brief 获取当前EPD刷新模式
 * @return 当前刷新模式
 */
int epd_get_refresh_mode(void)
{
    return g_epd_refresh_mode;
}

/**
 * @brief 写入LUT表到EPD控制器
 * @details 根据当前刷新模式选择相应的LUT表写入
 */
static void epd_write_lut_tables(void)
{
    int j;

    // 写入R20寄存器LUT
    epd_write_cmd(0x20);
    for (j = 0; j < 56; j++) {
        if (g_epd_refresh_mode == EPD_REFRESH_MODE_GC) {
            epd_write_data(lut_R20_GC[j]);
        } else {
            epd_write_data(lut_R20_DU[j]);
        }
    }

    // 写入R21寄存器LUT
    epd_write_cmd(0x21);
    for (j = 0; j < 48; j++) {
        if (g_epd_refresh_mode == EPD_REFRESH_MODE_GC) {
            epd_write_data(lut_R24_GC[j]);
        } else {
            epd_write_data(lut_R24_DU[j]);
        }
    }

    // 写入R22寄存器LUT
    epd_write_cmd(0x22);
    for (j = 0; j < 56; j++) {
        if (g_epd_refresh_mode == EPD_REFRESH_MODE_GC) {
            epd_write_data(lut_R23_GC[j]);
        } else {
            epd_write_data(lut_R23_DU[j]);
        }
    }

    // 写入R23寄存器LUT
    epd_write_cmd(0x23);
    for (j = 0; j < 56; j++) {
        if (g_epd_refresh_mode == EPD_REFRESH_MODE_GC) {
            epd_write_data(lut_R22_GC[j]);
        } else {
            epd_write_data(lut_R22_DU[j]);
        }
    }

    // 写入R24寄存器LUT
    epd_write_cmd(0x24);
    for (j = 0; j < 56; j++) {
        if (g_epd_refresh_mode == EPD_REFRESH_MODE_GC) {
            epd_write_data(lut_R21_GC[j]);
        } else {
            epd_write_data(lut_R21_DU[j]);
        }
    }
}

/**
 * @brief 新屏幕EPD初始化函数
 * @details 基于370bw_code.c的LCD_initial函数实现
 * @param HRES 水平分辨率
 * @param VRES 垂直分辨率
 * @param HST 水平起始位置
 * @param VST 垂直起始位置
 * @return EPD状态
 */
epd_status_t epd_display_init_new(unsigned int HRES, unsigned int VRES,
                                  unsigned int HST, unsigned int VST,
                                  int skip_hardware_reset)
{
    printf("EPD new screen init start (HRES=%d, VRES=%d, skip_reset=%d)...\r\n",
           HRES, VRES, skip_hardware_reset);

    // ========== 步骤1: 条件性硬件复位 ==========
    if (!skip_hardware_reset) {
        printf("EPD: Step 1 - Hardware reset\r\n");
        epd_reset();
    } else {
        printf("EPD: Step 1 - Skipping hardware reset (already done)\r\n");
    }

    // ========== 步骤2: 0x00 Panel Setting ==========
    printf("EPD: Step 2 - Panel Setting (0x00)\r\n");
    epd_write_cmd(0x00);
    epd_write_data(0xf7);
    epd_write_data(0x0d);

    // ========== 步骤3: 0x01 Power Setting ==========
    printf("EPD: Step 3 - Power Setting (0x01)\r\n");
    epd_write_cmd(0x01);
    epd_write_data(0x03);
    epd_write_data(0x00);
    epd_write_data(0x3f);
    epd_write_data(0x3f);
    epd_write_data(0x1f);

    // 0x03: Power Off Sequence Setting
    epd_write_cmd(0x03);
    epd_write_data(0x00);

    // 0x06: Booster Soft Start
    epd_write_cmd(0x06);
    epd_write_data(0x97);
    epd_write_data(0x97);
    epd_write_data(0x23);

    // 0x30: PLL Control
    epd_write_cmd(0x30);
    epd_write_data(0x09);

    // 0x50: VCOM AND DATA INTERVAL SETTING
    epd_write_cmd(0x50);
    epd_write_data(0x87);

    // 0x60: TCON SETTING
    epd_write_cmd(0x60);
    epd_write_data(0x22);

    // 0x82: VCM_DC_SETTING
    epd_write_cmd(0x82);
    epd_write_data(0x00);

    // 0xe3: POWER SAVING
    epd_write_cmd(0xe3);
    epd_write_data(0x00);

    // 0x41: Temperature Sensor Selection
    epd_write_cmd(0x41);
    epd_write_data(0x0f);

    // 0x61: Resolution Setting
    epd_write_cmd(0x61);
    epd_write_data(0xf0);  // 240 pixels width (0xf0 = 240)
    epd_write_data(0x01);  // High byte of height
    epd_write_data(0xA0);  // Low byte of height (0x01A0 = 416)

    // 0x65: Resolution Setting
    epd_write_cmd(0x65);
    epd_write_data(0x00);
    epd_write_data(0x00);
    epd_write_data(0x00);

    // 写入LUT表
    epd_write_lut_tables();

    g_epd_power_state = EPD_POWER_STATE_ACTIVE;
    printf("EPD new screen initialized successfully\r\n");
    return EPD_STATUS_OK;
}

/**
 * @brief 填充EPD RAM
 * @details 基于370bw_code.c的Fill_RAM函数实现
 * @param data 填充数据
 * @param h_line 水平像素数
 * @param v_line 垂直像素数
 */
void epd_fill_ram_new(unsigned char data, unsigned int h_line, unsigned int v_line)
{
    unsigned int total_bytes = (h_line * v_line) / 8;
    unsigned int j;

    printf("EPD filling RAM with data=0x%02X, size=%d bytes\r\n", data, total_bytes);

    // 0x13: Data Start Transmission 1
    epd_write_cmd(0x13);
    for (j = 0; j < total_bytes; j++) {
        epd_write_data(data);
    }

    // 刷新显示
    epd_write_cmd(0x04);  // Power On
    if (epd_check_busy() != EPD_STATUS_OK) { return; }

    epd_write_cmd(0x12);  // Display Refresh
    if (epd_check_busy() != EPD_STATUS_OK) { return; }

    epd_write_cmd(0x02);  // Power Off
    if (epd_check_busy() != EPD_STATUS_OK) { return; }

    printf("EPD RAM fill completed\r\n");
}

/**
 * @brief 新屏幕EPD显示初始化（简化版本）
 * @details 使用默认参数初始化EPD
 * @return EPD状态
 */
epd_status_t epd_display_init_new_simple(void)
{
    // 使用默认分辨率 240x416，正常执行硬件重置
    return epd_display_init_new(240, 416, 0, 0, 0); // skip_hardware_reset=0
}

/**
 * @brief 新屏幕EPD清屏
 * @details 将整个屏幕填充为白色
 * @return EPD状态
 */
epd_status_t epd_display_clear_new(void)
{
    printf("EPD clearing screen...\r\n");

    // 确保EPD已初始化
    if (g_epd_power_state == EPD_POWER_STATE_UNINITIALIZED) {
        epd_status_t ret = epd_display_init_new_simple();
        if (ret != EPD_STATUS_OK) {
            return ret;
        }
    }

    // 填充白色 (0xFF表示全白)
    epd_fill_ram_new(0xFF, 240, 416);

    printf("EPD screen cleared\r\n");
    return EPD_STATUS_OK;
}

/**
 * @brief 新屏幕EPD测试函数
 * @details 执行基本的显示测试
 * @return EPD状态
 */
epd_status_t epd_display_test_new(void)
{
    epd_status_t ret;

    printf("EPD new screen test starting...\r\n");

    // 初始化
    ret = epd_display_init_new_simple();
    if (ret != EPD_STATUS_OK) {
        printf("EPD init failed\r\n");
        return ret;
    }

    // 清屏
    ret = epd_display_clear_new();
    if (ret != EPD_STATUS_OK) {
        printf("EPD clear failed\r\n");
        return ret;
    }

    printf("EPD new screen test completed successfully\r\n");
    return EPD_STATUS_OK;
}

/**
 * @brief 获取EPD电源状态
 * @return EPD电源状态
 */
epd_power_state_t epd_get_power_state_new(void)
{
    return g_epd_power_state;
}

/**
 * @brief 将图像缓冲区数据传输到EPD显示
 * @details 将内存中的图像数据传输到EPD进行显示
 * @param image_buffer 图像缓冲区指针
 * @param width 图像宽度
 * @param height 图像高度
 * @return EPD状态
 */
epd_status_t epd_display_image_buffer_new(const unsigned char* image_buffer,
                                          unsigned int width, unsigned int height)
{
    if (image_buffer == NULL) {
        printf("EPD image buffer is NULL\r\n");
        return EPD_STATUS_ERROR;
    }

    if (width != 240 || height != 416) {
        printf("EPD image size mismatch: %dx%d (expected 240x416)\r\n", width, height);
        return EPD_STATUS_ERROR;
    }

    unsigned int total_bytes = (width * height) / 8;
    unsigned int j;

    printf("EPD transmitting image buffer (%d bytes)...\r\n", total_bytes);

    // 确保EPD已初始化
    if (g_epd_power_state == EPD_POWER_STATE_UNINITIALIZED) {
        epd_status_t ret = epd_display_init_new_simple();
        if (ret != EPD_STATUS_OK) {
            return ret;
        }
    }

    // 0x13: Data Start Transmission 1
    epd_write_cmd(0x13);
    for (j = 0; j < total_bytes; j++) {
        epd_write_data(image_buffer[j]);
    }

    // 刷新显示
    epd_write_cmd(0x04);  // Power On
    if (epd_check_busy() != EPD_STATUS_OK) { return EPD_STATUS_ERROR; }

    epd_write_cmd(0x12);  // Display Refresh
    if (epd_check_busy() != EPD_STATUS_OK) { return EPD_STATUS_ERROR; }

    epd_write_cmd(0x02);  // Power Off
    if (epd_check_busy() != EPD_STATUS_OK) { return EPD_STATUS_ERROR; }

    printf("EPD image buffer transmission completed\r\n");
    return EPD_STATUS_OK;
}

// ========== 统一架构：新增局部刷新功能实现 ==========

/**
 * @brief 初始化新屏幕驱动上下文
 */
epd_status_t epd_new_context_init(void)
{
    memset(&g_new_ctx, 0, sizeof(g_new_ctx));
    g_new_ctx.partial_flag = 1;  // 标记为首次刷新
    g_new_ctx.refresh_count = 0;
    g_new_ctx.last_refresh_type = EPD_REFRESH_TYPE_STANDARD_FULL;

    printf("EPD new context initialized\r\n");
    return EPD_STATUS_OK;
}

/**
 * @brief 获取区域坐标信息
 */
const epd_area_coord_t* epd_get_area_coord_new(epd_refresh_area_t area)
{
    if (area < EPD_AREA_QUESTION || area > EPD_AREA_FULL) {
        return NULL;
    }
    return &g_area_coords[area];
}

/**
 * @brief 硬件重置（新屏幕版本）
 */
epd_status_t epd_reset_new(void)
{
    printf("EPD hardware reset (new screen)\r\n");

    // 执行硬件重置
    epd_reset();

    // 重置后延时，确保引脚状态稳定
    epd_spi_delay_us(5 * 1000);  // 10ms = 50000us

    // 重新初始化EPD（跳过硬件重置，因为刚刚已经执行过）
    epd_status_t ret = epd_display_init_new(240, 416, 0, 0, 1); // skip_hardware_reset=1
    if (ret != EPD_STATUS_OK) {
        printf("EPD reset failed - reinit error\r\n");
        return ret;
    }

    printf("EPD hardware reset completed\r\n");
    return EPD_STATUS_OK;
}

/**
 * @brief 设置局部刷新窗口
 */
epd_status_t epd_set_partial_window_new(epd_refresh_area_t area)
{
    unsigned int x_start, x_end, y_start, y_end;
    epd_status_t ret = epd_convert_coordinates_simple(area, &x_start, &x_end, &y_start, &y_end);
    if (ret != EPD_STATUS_OK) {
        printf("EPD: Failed to convert coordinates for area %d\r\n", area);
        return ret;
    }

    printf("EPD: Setting partial window (simple pixel coordinate format)\r\n");

    // 进入局部模式
    epd_write_cmd(0x91);  // PTIN

    // 设置局部窗口 - 传统像素坐标格式（参照Demo_epd_du的0x90命令格式）
    epd_write_cmd(0x90);  // PTL
    epd_write_data(x_start);                    // X起始像素
    epd_write_data(x_end);                      // X结束像素
    epd_write_data((y_start >> 8) & 0xFF);      // Y起始高字节
    epd_write_data(y_start & 0xFF);             // Y起始低字节
    epd_write_data((y_end >> 8) & 0xFF);        // Y结束高字节
    epd_write_data(y_end & 0xFF);               // Y结束低字节
    epd_write_data(0x01);                       // 扫描方向

    printf("EPD: PTL command sent - 0x90 [%d,%d,%d,%d,%d,%d,0x01]\r\n",
           x_start, x_end,
           (y_start >> 8) & 0xFF, y_start & 0xFF,
           (y_end >> 8) & 0xFF, y_end & 0xFF);

    printf("EPD: Traditional pixel coordinate format applied\r\n");
    return EPD_STATUS_OK;
}

/**
 * @brief UC8253c完整局部刷新序列
 * @param area 刷新区域
 * @param new_data 新图像数据
 * @param data_size 数据大小
 * @return EPD状态
 */
static epd_status_t epd_partial_refresh_uc8253c_complete(epd_refresh_area_t area,
                                                        const unsigned char* new_data,
                                                        unsigned int data_size)
{
    printf("EPD: Starting UC8253c complete partial refresh\r\n");

    // 步骤1: 配置PSR使用寄存器LUT
    epd_status_t ret = epd_configure_psr_for_register_lut();
    if (ret != EPD_STATUS_OK) return ret;

    // 步骤2: 加载DU模式LUT
    epd_write_lut_tables();  // 使用现有的DU模式LUT
    printf("EPD: DU mode LUT tables loaded\r\n");

    // 步骤3: 电源开启
    epd_write_cmd(0x04);  // PON
    ret = epd_check_busy();
    if (ret != EPD_STATUS_OK) return ret;

    // 步骤4: 设置局部窗口 (正确格式)
    ret = epd_set_partial_window_new(area);
    if (ret != EPD_STATUS_OK) return ret;

    // 步骤5: 验证局部模式
    ret = epd_verify_partial_mode_uc8253c();
    if (ret != EPD_STATUS_OK) {
        printf("EPD: Warning - partial mode verification failed, continuing...\r\n");
        // 不返回错误，继续执行
    }

    // 步骤6: 传输OLD数据 (KW模式)
    printf("EPD: Transmitting OLD data (0x10)\r\n");
    epd_write_cmd(0x10);  // DTM1

    // 获取对应区域的旧数据缓存
    unsigned char* old_data_cache = NULL;
    switch (area) {
        case EPD_AREA_QUESTION:
            old_data_cache = g_new_ctx.question_old_data;
            break;
        case EPD_AREA_ANSWER:
            old_data_cache = g_new_ctx.answer_old_data;
            break;
        default:
            printf("EPD: Unsupported area %d\r\n", area);
            return EPD_STATUS_ERROR;
    }

    for (unsigned int i = 0; i < data_size; i++) {
        epd_write_data(old_data_cache[i]);
    }

    // 步骤7: 传输NEW数据 (KW模式)
    printf("EPD: Transmitting NEW data (0x13)\r\n");
    epd_write_cmd(0x13);  // DTM2
    for (unsigned int i = 0; i < data_size; i++) {
        epd_write_data(new_data[i]);
        old_data_cache[i] = new_data[i];  // 更新缓存
    }

    // 步骤8: 执行刷新
    printf("EPD: Executing display refresh (0x12)\r\n");
    epd_write_cmd(0x12);  // DRF
    ret = epd_check_busy();
    if (ret != EPD_STATUS_OK) return ret;

    // 步骤9: 退出局部模式
    epd_write_cmd(0x92);  // PTOUT

    // 步骤10: 电源关闭
    epd_write_cmd(0x02);  // POF
    ret = epd_check_busy();
    if (ret != EPD_STATUS_OK) return ret;

    printf("EPD: UC8253c partial refresh completed successfully\r\n");
    return EPD_STATUS_OK;
}

/**
 * @brief 新屏幕局部刷新（支持0x10+0x13差分刷新）
 */
epd_status_t epd_display_partial_new(epd_refresh_area_t area,
                                     const unsigned char* new_data,
                                     unsigned int data_size,
                                     epd_refresh_type_t refresh_type)
{
    if (new_data == NULL || data_size == 0) {
        printf("EPD partial refresh: invalid parameters\r\n");
        return EPD_STATUS_ERROR;
    }

    printf("EPD: Starting partial refresh - area=%d, type=%d, size=%d\r\n",
           area, refresh_type, data_size);

    // 验证数据大小
    unsigned int max_cache_size;
    switch (area) {
        case EPD_AREA_QUESTION:
            max_cache_size = sizeof(g_new_ctx.question_old_data);
            break;
        case EPD_AREA_ANSWER:
            max_cache_size = sizeof(g_new_ctx.answer_old_data);
            break;
        default:
            printf("EPD: Unsupported area %d\r\n", area);
            return EPD_STATUS_ERROR;
    }

    if (data_size > max_cache_size) {
        printf("EPD: Data size %d exceeds cache size %d\r\n", data_size, max_cache_size);
        return EPD_STATUS_ERROR;
    }

    // 使用UC8253c完整局部刷新序列
    return epd_partial_refresh_uc8253c_complete(area, new_data, data_size);
}

/**
 * @brief 测试UC8253c局部刷新功能 (调试用)
 * @return EPD状态
 */
epd_status_t epd_test_uc8253c_partial_refresh(void)
{
    printf("=== UC8253c Partial Refresh Test ===\r\n");

    // 测试1: 左上角小窗口
    printf("\n--- Test 1: Top-left corner (16x32 pixels) ---\r\n");
    epd_status_t ret = epd_test_simple_partial_window(0, 1, 0, 31);
    if (ret != EPD_STATUS_OK) {
        printf("Test 1 failed: %d\r\n", ret);
        return ret;
    }

    // 延时2秒观察效果
    epd_spi_delay_us(2000 * 1000);

    // 测试2: 中心区域
    printf("\n--- Test 2: Center area (64x64 pixels) ---\r\n");
    ret = epd_test_simple_partial_window(10, 17, 176, 239);  // 中心64x64区域
    if (ret != EPD_STATUS_OK) {
        printf("Test 2 failed: %d\r\n", ret);
        return ret;
    }

    // 延时2秒观察效果
    epd_spi_delay_us(2000 * 1000);

    // 测试3: 问题区域的实际坐标
    printf("\n--- Test 3: Question area coordinates ---\r\n");
    ret = epd_test_simple_partial_window(0, 29, 85, 142);  // 问题区域
    if (ret != EPD_STATUS_OK) {
        printf("Test 3 failed: %d\r\n", ret);
        return ret;
    }

    printf("=== UC8253c Partial Refresh Test Completed ===\r\n");
    return EPD_STATUS_OK;
}

/**
 * @brief 统一EPD刷新接口（核心函数）
 */
epd_status_t epd_refresh_unified_new(epd_refresh_type_t type,
                                     epd_refresh_area_t area,
                                     const unsigned char* data,
                                     unsigned int data_size)
{
    if (data == NULL || data_size == 0) {
        printf("EPD unified refresh: invalid parameters\r\n");
        return EPD_STATUS_ERROR;
    }

    printf("EPD unified refresh: type=%d, area=%d, size=%d\r\n", type, area, data_size);

    // 确保EPD已初始化
    if (g_epd_power_state == EPD_POWER_STATE_UNINITIALIZED) {
        epd_status_t ret = epd_display_init_new_simple();
        if (ret != EPD_STATUS_OK) {
            printf("EPD unified refresh: init failed\r\n");
            return ret;
        }
    }

    // 确保上下文已初始化
    if (g_new_ctx.refresh_count == 0 && g_new_ctx.partial_flag == 0) {
        epd_new_context_init();
    }

    epd_status_t ret = EPD_STATUS_OK;

    switch (type) {
        case EPD_REFRESH_TYPE_STANDARD_FULL:
            // 标准全屏刷新：GC模式，高质量
            printf("EPD executing standard full refresh (GC mode)\r\n");
            epd_set_refresh_mode(EPD_REFRESH_MODE_GC);
            ret = epd_display_image_buffer_new(data, 240, 416);
            break;

        case EPD_REFRESH_TYPE_FAST_FULL:
            // 快速全屏刷新：DU模式，快速
            printf("EPD executing fast full refresh (DU mode)\r\n");
            epd_set_refresh_mode(EPD_REFRESH_MODE_DU);
            ret = epd_display_image_buffer_new(data, 240, 416);
            break;

        case EPD_REFRESH_TYPE_SIMPLE_PARTIAL:
            // 简单局部刷新：不重置，快速更新
            printf("EPD executing simple partial refresh\r\n");
            epd_set_refresh_mode(EPD_REFRESH_MODE_DU);
            ret = epd_display_partial_new(area, data, data_size, type);
            break;

        case EPD_REFRESH_TYPE_MULTI_PARTIAL:
            // 多区域局部刷新：强制硬件重置，确保可靠更新
            printf("EPD executing multi-area partial refresh with mandatory hardware reset\r\n");

            // 1. 强制硬件重置
            printf("EPD: Performing mandatory hardware reset for multi-partial refresh\r\n");
            epd_status_t reset_ret = epd_reset_new();
            if (reset_ret != EPD_STATUS_OK) {
                printf("EPD: Hardware reset failed: %d\r\n", reset_ret);
                return reset_ret;
            }
            printf("EPD: Hardware reset completed successfully\r\n");

            // 2. 设置刷新模式
            epd_set_refresh_mode(EPD_REFRESH_MODE_DU);

            // 3. 执行局部刷新
            ret = epd_display_partial_new(area, data, data_size, type);
            break;

        case EPD_REFRESH_TYPE_S37T03_PARTIAL:
            // S-GDEY037T03风格局部刷新：高效无重置版本（14.6倍性能提升）
            printf("EPD executing S-GDEY037T03 style partial refresh (high performance)\r\n");

            // 直接执行S37T03风格局部刷新（无硬件重置，仿照原始demo）
            ret = epd_partial_refresh_s37t03_style(area, data, data_size);
            if (ret != EPD_STATUS_OK) {
                printf("EPD: S37T03 style refresh failed: %d, trying force reinit\r\n", ret);
                // 失败时尝试重新初始化
                epd_status_t reinit_ret = epd_s37t03_force_reinit();
                if (reinit_ret == EPD_STATUS_OK) {
                    ret = epd_partial_refresh_s37t03_style(area, data, data_size);
                }
            }
            break;

        default:
            printf("EPD unified refresh: invalid type %d\r\n", type);
            return EPD_STATUS_ERROR;
    }

    if (ret == EPD_STATUS_OK) {
        g_new_ctx.last_refresh_type = type;
        printf("EPD unified refresh completed successfully\r\n");
    } else {
        printf("EPD unified refresh failed: %d\r\n", ret);
    }

    return ret;
}

/**
 * @brief 简化的局部刷新（结合两个demo的优点）
 * @param area 刷新区域
 * @param data 显示数据（NULL则使用预定义模板）
 * @return EPD状态
 */
epd_status_t epd_partial_refresh_unified(epd_refresh_area_t area, const unsigned char* data)
{
    unsigned int data_size;
    const unsigned char* display_data;

    if (data != NULL) {
        // 使用用户提供的数据
        display_data = data;
        // 根据区域计算数据大小
        if (area == EPD_AREA_QUESTION) {
            data_size = EPD_QUESTION_DATA_SIZE;
        } else if (area == EPD_AREA_ANSWER) {
            data_size = EPD_ANSWER_DATA_SIZE;
        } else {
            printf("EPD: Invalid area for unified refresh\r\n");
            return EPD_STATUS_INVALID_PARAM;
        }
    } else {
        // 使用预定义模板数据
        display_data = get_area_display_data(area, &data_size);
        if (display_data == NULL) {
            printf("EPD: Failed to get display data for area %d\r\n", area);
            return EPD_STATUS_ERROR;
        }
    }

    printf("EPD: Starting unified partial refresh for area %d\r\n", area);

    // 确保EPD已初始化
    if (g_epd_power_state == EPD_POWER_STATE_UNINITIALIZED) {
        epd_status_t init_ret = epd_display_init_new_simple();
        if (init_ret != EPD_STATUS_OK) {
            printf("EPD: Unified refresh init failed\r\n");
            return init_ret;
        }
    }

    // 设置窗口（使用简化的像素坐标方式）
    epd_status_t ret = epd_set_partial_window_new(area);
    if (ret != EPD_STATUS_OK) {
        printf("EPD: Failed to set partial window\r\n");
        return ret;
    }

    // 数据传输（参照Demo_epd_du的方式）
    printf("EPD: Transmitting OLD data (%d bytes)\r\n", data_size);
    epd_write_cmd(0x10);
    for (unsigned int i = 0; i < data_size; i++) {
        epd_write_data(0xFF);  // 全白背景
    }

    printf("EPD: Transmitting NEW data (%d bytes)\r\n", data_size);
    epd_write_cmd(0x13);
    for (unsigned int i = 0; i < data_size; i++) {
        epd_write_data(display_data[i]);
    }

    // 刷新显示
    printf("EPD: Starting display refresh\r\n");
    epd_write_cmd(0x12);
    ret = epd_check_busy();

    if (ret == EPD_STATUS_OK) {
        printf("EPD: Unified partial refresh completed successfully\r\n");
    } else {
        printf("EPD: Unified partial refresh failed\r\n");
    }

    return ret;
}
