Archive member included to satisfy reference by file (symbol)

../../../../../include_lib/newlib/pi32v2-lib/libc.a(lib_a-memcmp.o)
                              obj/Release/apps/common/update/update.o (symbol from plugin) (memcmp)
../../../../../include_lib/newlib/pi32v2-lib/libc.a(lib_a-strcmp.o)
                              obj/Release/apps/common/net/assign_macaddr.o (symbol from plugin) (strcmp)
../../../../../include_lib/newlib/pi32v2-lib/libc.a(lib_a-strcpy.o)
                              obj/Release/apps/common/net/assign_macaddr.o (symbol from plugin) (strcpy)
../../../../../include_lib/newlib/pi32v2-lib/libc.a(lib_a-strlen.o)
                              obj/Release/apps/common/net/config_network.o (symbol from plugin) (strlen)
../../../../../include_lib/newlib/pi32v2-lib/libc.a(lib_a-strncmp.o)
                              obj/Release/apps/common/net/config_network.o (symbol from plugin) (strncmp)
../../../../../include_lib/newlib/pi32v2-lib/libc.a(lib_a-strncpy.o)
                              obj/Release/apps/common/net/assign_macaddr.o (symbol from plugin) (strncpy)
../../../../../include_lib/newlib/pi32v2-lib/libc.a(lib_a-strstr.o)
                              obj/Release/apps/common/net/assign_macaddr.o (symbol from plugin) (strstr)
../../../../../cpu/wl82/liba/cpu.a(uart.c.o)
                              obj/Release/apps/demo/demo_wifi/board/wl82/board.o (symbol from plugin) (uart_init)
../../../../../cpu/wl82/liba/cpu.a(delay.c.o)
                              obj/Release/apps/common/iic/software_iic.o (symbol from plugin) (delay)
../../../../../cpu/wl82/liba/cpu.a(wdt.c.o)
                              obj/Release/apps/common/update/update.o (symbol from plugin) (wdt_clear)
../../../../../cpu/wl82/liba/cpu.a(crc16.c.o)
                              obj/Release/cpu/wl82/setup.o (symbol from plugin) (__crc16_mutex_init)
../../../../../cpu/wl82/liba/cpu.a(clock.c.o)
                              delay.c.o (symbol from plugin) (sys_clk_get)
../../../../../cpu/wl82/liba/cpu.a(gpio.c.o)
                              obj/Release/apps/common/iic/software_iic.o (symbol from plugin) (gpio_port_lock)
../../../../../cpu/wl82/liba/cpu.a(rtc.c.o)
                              gpio.c.o (symbol from plugin) (rtc_port_pr_in)
../../../../../cpu/wl82/liba/cpu.a(usb.c.o)
                              clock.c.o (symbol from plugin) (usb_pll_init)
../../../../../cpu/wl82/liba/cpu.a(timer.c.o)
                              obj/Release/apps/demo/demo_wifi/epd_touch/test/epd_touch_test.o (symbol from plugin) (timer_get_ms)
../../../../../cpu/wl82/liba/cpu.a(spec_uart.c.o)
                              obj/Release/apps/demo/demo_wifi/board/wl82/board.o (symbol from plugin) (uart_dev_ops)
../../../../../cpu/wl82/liba/cpu.a(spi0.c.o)
                              obj/Release/apps/demo/demo_wifi/board/wl82/board.o (symbol from plugin) (spi_dev_ops)
../../../../../cpu/wl82/liba/cpu.a(lrc_hw.c.o)
                              rtc.c.o (symbol from plugin) (__get_lrc_osc_hz)
../../../../../cpu/wl82/liba/cpu.a(p33.c.o)
                              rtc.c.o (symbol from plugin) (p33_lock)
../../../../../cpu/wl82/liba/cpu.a(power_hw.c.o)
                              obj/Release/cpu/wl82/setup.o (symbol from plugin) (p33_io_latch_init)
../../../../../cpu/wl82/liba/cpu.a(power_wakeup.c.o)
                              power_hw.c.o (symbol from plugin) (long_press_io_keep)
../../../../../cpu/wl82/liba/cpu.a(dac.c.o)
                              power_hw.c.o (symbol from plugin) (dac_power_off)
../../../../../cpu/wl82/liba/cpu.a(vm_sfc.c.o)
                              power_hw.c.o (symbol from plugin) (dsp_corex_enter_hold)
../../../../../cpu/wl82/liba/cpu.a(otg.c.o)
                              usb.c.o (symbol from plugin) (otg_prevent_detect)
../../../../../cpu/wl82/liba/cpu.a(usb_phy.c.o)
                              usb.c.o (symbol from plugin) (get_jiffies)
../../../../../cpu/wl82/liba/cpu.a(device_api.c.o)
                              obj/Release/apps/demo/demo_wifi/epd_touch/epd/epd_spi.o (symbol from plugin) (dev_open)
../../../../../cpu/wl82/liba/cpu.a(startup.S.o)
                              (_start)
../../../../../cpu/wl82/liba/cpu.a(version.z.o)
                              (lib_driver_version)
../../../../../cpu/wl82/liba/cpu.a(boot.c.o)
                              ../../../../../cpu/wl82/liba/cpu.a(startup.S.o) (boot_memmove)
../../../../../cpu/wl82/liba/cpu.a(cache.c.o)
                              power_hw.c.o (symbol from plugin) (flush_all_cache)
../../../../../cpu/wl82/liba/cpu.a(encryption.c.o)
                              vm_sfc.c.o (symbol from plugin) (doe)
../../../../../cpu/wl82/liba/cpu.a(lp_timer_hw.c.o)
                              power_hw.c.o (symbol from plugin) (__hw_nv_timer_get_time)
../../../../../cpu/wl82/liba/cpu.a(key0_decode.c.o)
                              boot.c.o (symbol from plugin) (mkey_dummy_func)
../../../../../cpu/wl82/liba/fs.a(sdfile_new.c.o)
                              (sdfile_vfs_ops)
../../../../../cpu/wl82/liba/fs.a(sdfile_ext.c.o)
                              sdfile_new.c.o (symbol from plugin) (sdfile_ext_cpu_addr2flash_addr)
../../../../../cpu/wl82/liba/fs.a(version.z.o)
                              (lib_fs_version)
../../../../../cpu/wl82/liba/fs.a(vfs.c.o)
                              sdfile_new.c.o (symbol from plugin) (mount)
../../../../../cpu/wl82/liba/fs.a(fat_compact.c.o)
                              vfs.c.o (symbol from plugin) (__fat_fscan_subpath_or_pre_dir)
../../../../../cpu/wl82/liba/fs.a(UTF8Unicode_conversion.c.o)
                              fat_compact.c.o (symbol from plugin) (utf8_check)
../../../../../cpu/wl82/liba/fs.a(mbr.c.o)
                              fat_compact.c.o (symbol from plugin) (mbr_scan)
../../../../../cpu/wl82/liba/fs.a(tff.c.o)
                              mbr.c.o (symbol from plugin) (ld_word_func)
../../../../../cpu/wl82/liba/fs.a(ffunicode.c.o)
                              tff.c.o (symbol from plugin) (ff_w_toupper)
../../../../../cpu/wl82/liba/fs.a(ff_opr.c.o)
                              fat_compact.c.o (symbol from plugin) (ff_update_saveinfo)
../../../../../cpu/wl82/liba/event.a(net_event.c.o)
                              obj/Release/apps/common/net/config_network.o (symbol from plugin) (net_event_notify)
../../../../../cpu/wl82/liba/event.a(device_event.c.o)
                              otg.c.o (symbol from plugin) (device_event_notify)
../../../../../cpu/wl82/liba/event.a(version.z.o)
                              (lib_event_version)
../../../../../cpu/wl82/liba/event.a(event.c.o)
                              net_event.c.o (symbol from plugin) (sys_event_notify)
../../../../../cpu/wl82/liba/system.a(app_core.c.o)
                              obj/Release/apps/common/system/init.o (symbol from plugin) (app_core_msg_handler)
../../../../../cpu/wl82/liba/system.a(ASCII_lib.c.o)
                              tff.c.o (symbol from plugin) (ASCII_ToUpper)
../../../../../cpu/wl82/liba/system.a(circular_buf.c.o)
                              event.c.o (symbol from plugin) (cbuf_read)
../../../../../cpu/wl82/liba/system.a(hwi.c.o)
                              obj/Release/apps/common/system/init.o (symbol from plugin) (dump_cpu_irq_usage)
../../../../../cpu/wl82/liba/system.a(port.c.o)
                              obj/Release/apps/common/system/init.o (symbol from plugin) (cpu1_run_flag)
../../../../../cpu/wl82/liba/system.a(movable.c.o)
                              obj/Release/cpu/wl82/setup.o (symbol from plugin) (code_movable_init)
../../../../../cpu/wl82/liba/system.a(tasks.c.o)
                              port.c.o (symbol from plugin) (xTaskSwitchContext)
../../../../../cpu/wl82/liba/system.a(main.c.o)
                              tasks.c.o (symbol from plugin) (vApplicationIdleHook)
../../../../../cpu/wl82/liba/system.a(task_status.c.o)
                              obj/Release/apps/common/system/init.o (symbol from plugin) (get_task_state)
../../../../../cpu/wl82/liba/system.a(os_api.c.o)
                              obj/Release/apps/common/system/init.o (symbol from plugin) (os_init)
../../../../../cpu/wl82/liba/system.a(malloc.c.o)
                              obj/Release/apps/common/net/assign_macaddr.o (symbol from plugin) (malloc)
../../../../../cpu/wl82/liba/system.a(printf-stdarg.c.o)
                              obj/Release/apps/common/net/assign_macaddr.o (symbol from plugin) (snprintf)
../../../../../cpu/wl82/liba/system.a(puthex.c.o)
                              obj/Release/apps/common/net/wifi_conf.o (symbol from plugin) (putchar)
../../../../../cpu/wl82/liba/system.a(log.c.o)
                              obj/Release/cpu/wl82/debug.o (symbol from plugin) (log_flush)
../../../../../cpu/wl82/liba/system.a(task.c.o)
                              os_api.c.o (symbol from plugin) (get_task_priority)
../../../../../cpu/wl82/liba/system.a(timer.c.o)
                              obj/Release/apps/common/system/init.o (symbol from plugin) (sys_timer_add)
../../../../../cpu/wl82/liba/system.a(wait_completion.c.o)
                              os_api.c.o (symbol from plugin) (wait_completion_before_kill_owner_task)
../../../../../cpu/wl82/liba/system.a(sys_timer_task.c.o)
                              obj/Release/apps/common/system/init.o (symbol from plugin) (sys_timer_task_init)
../../../../../cpu/wl82/liba/system.a(port_asm.S.o)
                              port.c.o (symbol from plugin) (vSoftwareInterruptISR)
../../../../../cpu/wl82/liba/system.a(movable.S.o)
                              movable.c.o (symbol from plugin) (movable_swi_entry)
../../../../../cpu/wl82/liba/system.a(version.z.o)
                              (lib_system_version)
../../../../../cpu/wl82/liba/system.a(port_overwrite.c.o)
                              tasks.c.o (symbol from plugin) (vPortSuppressTicksAndSleep)
../../../../../cpu/wl82/liba/system.a(list.c.o)
                              tasks.c.o (symbol from plugin) (vListInitialise)
../../../../../cpu/wl82/liba/system.a(queue.c.o)
                              os_api.c.o (symbol from plugin) (xQueueGenericReset)
../../../../../cpu/wl82/liba/system.a(event_groups.c.o)
                              os_api.c.o (symbol from plugin) (xEventGroupCreate)
../../../../../cpu/wl82/liba/system.a(lbuf.c.o)
                              log.c.o (symbol from plugin) (lbuf_init)
../../../../../cpu/wl82/liba/system.a(fcvt.c.o)
                              printf-stdarg.c.o (symbol from plugin) (ecvtbuf)
../../../../../cpu/wl82/liba/cfg_tool.a(vm.c.o)
                              sdfile_new.c.o (symbol from plugin) (sfc_erase_zone)
../../../../../cpu/wl82/liba/cfg_tool.a(syscfg_api.c.o)
                              obj/Release/apps/common/config/user_cfg.o (symbol from plugin) (syscfg_read)
../../../../../cpu/wl82/liba/cfg_tool.a(version.z.o)
                              (lib_cfg_tool_version)
../../../../../cpu/wl82/liba/cfg_tool.a(cfg_bin.c.o)
                              syscfg_api.c.o (symbol from plugin) (syscfg_bin_enable)
../../../../../cpu/wl82/liba/cfg_tool.a(cfg_btif.c.o)
                              syscfg_api.c.o (symbol from plugin) (syscfg_btif_enable)
../../../../../cpu/wl82/liba/common_lib.a(time.c.o)
                              rtc.c.o (symbol from plugin) (localtime_r)
../../../../../cpu/wl82/liba/common_lib.a(string.c.o)
                              obj/Release/apps/demo/demo_wifi/llm_json.o (symbol from plugin) (strdup)
../../../../../cpu/wl82/liba/common_lib.a(weak.c.o)
                              obj/Release/apps/common/net/config_network.o (symbol from plugin) (_swap16)
../../../../../cpu/wl82/liba/common_lib.a(Vsprintf.c.o)
                              obj/Release/apps/common/net/assign_macaddr.o (symbol from plugin) (sscanf)
../../../../../cpu/wl82/liba/common_lib.a(mktime.c.o)
                              rtc.c.o (symbol from plugin) (mktime)
../../../../../cpu/wl82/liba/common_lib.a(character_encoding.c.o)
                              vfs.c.o (symbol from plugin) (long_file_name_encode)
../../../../../cpu/wl82/liba/common_lib.a(version.z.o)
                              (lib_common_version)
../../../../../cpu/wl82/liba/common_lib.a(strtod.c.o)
                              Vsprintf.c.o (symbol from plugin) (strtod)
../../../../../cpu/wl82/liba/update.a(update_main.c.o)
                              obj/Release/apps/common/update/update.o (symbol from plugin) (update_module_init)
../../../../../cpu/wl82/liba/update.a(flash_fs_api.c.o)
                              obj/Release/apps/common/update/update.o (symbol from plugin) (vm_reverse_addr_size_get)
../../../../../cpu/wl82/liba/update.a(dual_bank_passive_update.c.o)
                              flash_fs_api.c.o (symbol from plugin) (flash_read_data_by_user_key)
../../../../../cpu/wl82/liba/update.a(reserved_zone_update.c.o)
                              dual_bank_passive_update.c.o (symbol from plugin) (reserved_zone_file_dual_bank_pasive_update_init)
../../../../../cpu/wl82/liba/update.a(dev_upgrade_api.c.o)
                              flash_fs_api.c.o (symbol from plugin) (get_app_boot_base_addr)
../../../../../cpu/wl82/liba/update.a(version.z.o)
                              (lib_update_version)
../../../../../cpu/wl82/liba/update.a(encrypt_api.c.o)
                              flash_fs_api.c.o (symbol from plugin) (decode_data_by_user_key)
../../../../../cpu/wl82/liba/update.a(download_loop.c.o)
                              update_main.c.o (symbol from plugin) (update_get_err_code)
../../../../../cpu/wl82/liba/wl_rf_common.a(wf_deep_sleep.c.o)
                              power_hw.c.o (symbol from plugin) (wf_set_phcom_cnt)
../../../../../cpu/wl82/liba/wl_rf_common.a(wl_sdio.c.o)
                              obj/Release/apps/common/net/wifi_conf.o (symbol from plugin) (wifi_rx_statistics_cont)
../../../../../cpu/wl82/liba/wl_rf_common.a(wl_hw_init.c.o)
                              wl_sdio.c.o (symbol from plugin) (wfhw_iqr_init)
../../../../../cpu/wl82/liba/wl_rf_common.a(wl_usb_to_sdio.c.o)
                              wl_sdio.c.o (symbol from plugin) (sdio_recv_int_hdl)
../../../../../cpu/wl82/liba/wl_rf_common.a(wl_sdio_host.c.o)
                              wl_sdio.c.o (symbol from plugin) (sdio_cap_irq)
../../../../../cpu/wl82/liba/wl_rf_common.a(wf_phy_mac_lib.c.o)
                              wl_hw_init.c.o (symbol from plugin) (bbp_set)
../../../../../cpu/wl82/liba/wl_rf_common.a(wf_phy_mac_init.c.o)
                              wl_hw_init.c.o (symbol from plugin) (wf_sifs_reset)
../../../../../cpu/wl82/liba/wl_rf_common.a(wf_cfr_init.c.o)
                              wl_hw_init.c.o (symbol from plugin) (rapp_init)
../../../../../cpu/wl82/liba/wl_rf_common.a(wf_rf_init.c.o)
                              wl_hw_init.c.o (symbol from plugin) (wf_rf_init)
../../../../../cpu/wl82/liba/wl_rf_common.a(wf_rf_cal.c.o)
                              wf_rf_init.c.o (symbol from plugin) (set_pll_rn_en)
../../../../../cpu/wl82/liba/wl_rf_common.a(wf_soft_adj.c.o)
                              wf_phy_mac_init.c.o (symbol from plugin) (dig_agc_tale_set)
../../../../../cpu/wl82/liba/wl_rf_common.a(wf_pa_dpd.c.o)
                              wf_rf_init.c.o (symbol from plugin) (pa_config_tune)
../../../../../cpu/wl82/liba/wl_rf_common.a(version.z.o)
                              (lib_wl82_rf_version)
../../../../../cpu/wl82/liba/wl_rf_common.a(wl_rfd_init.c.o)
                              wl_hw_init.c.o (symbol from plugin) (wl_anl_actl_init)
../../../../../cpu/wl82/liba/hsm.a(version.z.o)
                              (lib_hsm_version)
../../../../../cpu/wl82/liba/wpasupplicant.a(main_none.c.o)
                              obj/Release/apps/demo/demo_wifi/wifi_demo_task.o (symbol from plugin) (wifi_set_sta_connect_timeout)
../../../../../cpu/wl82/liba/wpasupplicant.a(scan.c.o)
                              main_none.c.o (symbol from plugin) (wpa_supplicant_req_scan)
../../../../../cpu/wl82/liba/wpasupplicant.a(wpa_supplicant.c.o)
                              scan.c.o (symbol from plugin) (wpa_supplicant_initiate_eapol)
../../../../../cpu/wl82/liba/wpasupplicant.a(wpas_glue.c.o)
                              wpa_supplicant.c.o (symbol from plugin) (wpa_supplicant_init_eapol)
../../../../../cpu/wl82/liba/wpasupplicant.a(sha1-pbkdf2.c.o)
                              wpa_supplicant.c.o (symbol from plugin) (pbkdf2_sha1)
../../../../../cpu/wl82/liba/wpasupplicant.a(sha1.c.o)
                              sha1-pbkdf2.c.o (symbol from plugin) (hmac_sha1_vector)
../../../../../cpu/wl82/liba/wpasupplicant.a(version.z.o)
                              (lib_wpa_supplicant_version)
../../../../../cpu/wl82/liba/wpasupplicant.a(hw_features_common.c.o)
                              wpa_supplicant.c.o (symbol from plugin) (hw_get_channel_chan)
../../../../../cpu/wl82/liba/wpasupplicant.a(ieee802_11_common.c.o)
                              hw_features_common.c.o (symbol from plugin) (ieee802_11_parse_elems)
../../../../../cpu/wl82/liba/wpasupplicant.a(wpa_common.c.o)
                              wpa_supplicant.c.o (symbol from plugin) (wpa_default_rsn_cipher)
../../../../../cpu/wl82/liba/wpasupplicant.a(md5.c.o)
                              wpa_common.c.o (symbol from plugin) (hmac_md5)
../../../../../cpu/wl82/liba/wpasupplicant.a(sha1-internal.c.o)
                              sha1.c.o (symbol from plugin) (sha1_vector)
../../../../../cpu/wl82/liba/wpasupplicant.a(sha1-prf.c.o)
                              wpa_common.c.o (symbol from plugin) (sha1_prf)
../../../../../cpu/wl82/liba/wpasupplicant.a(driver_common.c.o)
                              scan.c.o (symbol from plugin) (wpa_scan_results_free)
../../../../../cpu/wl82/liba/wpasupplicant.a(drivers.c.o)
                              wpa_supplicant.c.o (symbol from plugin) (wpa_drivers)
../../../../../cpu/wl82/liba/wpasupplicant.a(l2_packet_none.c.o)
                              wpa_supplicant.c.o (symbol from plugin) (l2_packet_get_own_addr)
../../../../../cpu/wl82/liba/wpasupplicant.a(wpa.c.o)
                              wpa_supplicant.c.o (symbol from plugin) (wpa_sm_rx_eapol)
../../../../../cpu/wl82/liba/wpasupplicant.a(wpa_ie.c.o)
                              wpa_supplicant.c.o (symbol from plugin) (wpa_parse_wpa_ie)
../../../../../cpu/wl82/liba/wpasupplicant.a(common.c.o)
                              ieee802_11_common.c.o (symbol from plugin) (hwaddr_aton)
../../../../../cpu/wl82/liba/wpasupplicant.a(eloop_none.c.o)
                              wpa_supplicant.c.o (symbol from plugin) (eloop_init)
../../../../../cpu/wl82/liba/wpasupplicant.a(os_none.c.o)
                              common.c.o (symbol from plugin) (os_get_time)
../../../../../cpu/wl82/liba/wpasupplicant.a(wpa_debug.c.o)
                              wpa_supplicant.c.o (symbol from plugin) (wpa_msg_register_ifname_cb)
../../../../../cpu/wl82/liba/wpasupplicant.a(wpabuf.c.o)
                              scan.c.o (symbol from plugin) (wpabuf_resize)
../../../../../cpu/wl82/liba/wpasupplicant.a(blacklist.c.o)
                              wpa_supplicant.c.o (symbol from plugin) (wpa_blacklist_get)
../../../../../cpu/wl82/liba/wpasupplicant.a(bss.c.o)
                              scan.c.o (symbol from plugin) (wpa_bss_update_start)
../../../../../cpu/wl82/liba/wpasupplicant.a(config.c.o)
                              wpa_supplicant.c.o (symbol from plugin) (wpa_config_free)
../../../../../cpu/wl82/liba/wpasupplicant.a(config_none.c.o)
                              main_none.c.o (symbol from plugin) (wpa_config_ssid_passphrase)
../../../../../cpu/wl82/liba/wpasupplicant.a(eap_register.c.o)
                              wpa_supplicant.c.o (symbol from plugin) (eap_register_methods)
../../../../../cpu/wl82/liba/wpasupplicant.a(events.c.o)
                              scan.c.o (symbol from plugin) (wpas_temp_disabled)
../../../../../cpu/wl82/liba/wpasupplicant.a(notify.c.o)
                              wpa_supplicant.c.o (symbol from plugin) (wpas_notify_supplicant_initialized)
../../../../../cpu/wl82/liba/wpasupplicant.a(op_classes.c.o)
                              wpa_supplicant.c.o (symbol from plugin) (wpas_supp_op_class_ie)
../../../../../cpu/wl82/liba/wpasupplicant.a(rrm.c.o)
                              wpa_supplicant.c.o (symbol from plugin) (wpas_rrm_reset)
../../../../../cpu/wl82/liba/wpasupplicant.a(wmm_ac.c.o)
                              events.c.o (symbol from plugin) (wmm_ac_notify_assoc)
../../../../../cpu/wl82/liba/wpasupplicant.a(aes-unwrap.c.o)
                              wpa.c.o (symbol from plugin) (aes_unwrap)
../../../../../cpu/wl82/liba/wpasupplicant.a(md5-internal.c.o)
                              md5.c.o (symbol from plugin) (md5_vector)
../../../../../cpu/wl82/liba/wpasupplicant.a(rc4.c.o)
                              wpa.c.o (symbol from plugin) (rc4_skip)
../../../../../cpu/wl82/liba/wpasupplicant.a(driver_wext.c.o)
                              drivers.c.o (symbol from plugin) (wpa_driver_wext_ops)
../../../../../cpu/wl82/liba/wpasupplicant.a(linux_ioctl.c.o)
                              driver_wext.c.o (symbol from plugin) (linux_set_iface_flags)
../../../../../cpu/wl82/liba/wpasupplicant.a(netlink.c.o)
                              driver_wext.c.o (symbol from plugin) (netlink_deinit)
../../../../../cpu/wl82/liba/wpasupplicant.a(bitfield.c.o)
                              rrm.c.o (symbol from plugin) (bitfield_alloc)
../../../../../cpu/wl82/liba/wpasupplicant.a(aes-internal-dec.c.o)
                              aes-unwrap.c.o (symbol from plugin) (aes_decrypt_init)
../../../../../cpu/wl82/liba/wpasupplicant.a(aes-internal.c.o)
                              aes-internal-dec.c.o (symbol from plugin) (rijndaelKeySetupEnc)
../../../../../cpu/wl82/liba/http_cli.a(http_cli.c.o)
                              obj/Release/apps/common/net/assign_macaddr.o (symbol from plugin) (httpcli_get)
../../../../../cpu/wl82/liba/http_cli.a(https_cli.c.o)
                              http_cli.c.o (symbol from plugin) (net_sock_ops_https)
../../../../../cpu/wl82/liba/http_cli.a(version.z.o)
                              (lib_httpcli_version)
../../../../../cpu/wl82/liba/json.a(json_tokener.c.o)
                              obj/Release/apps/common/net/assign_macaddr.o (symbol from plugin) (json_tokener_parse)
../../../../../cpu/wl82/liba/json.a(json_util.c.o)
                              json_tokener.c.o (symbol from plugin) (json_parse_double)
../../../../../cpu/wl82/liba/json.a(json_object.c.o)
                              json_tokener.c.o (symbol from plugin) (json_object_get)
../../../../../cpu/wl82/liba/json.a(arraylist.c.o)
                              json_object.c.o (symbol from plugin) (array_list_new)
../../../../../cpu/wl82/liba/json.a(printbuf.c.o)
                              json_tokener.c.o (symbol from plugin) (printbuf_new)
../../../../../cpu/wl82/liba/json.a(linkhash.c.o)
                              json_object.c.o (symbol from plugin) (lh_kchar_table_new)
../../../../../cpu/wl82/liba/json.a(debug.c.o)
                              json_tokener.c.o (symbol from plugin) (mc_debug)
../../../../../cpu/wl82/liba/json.a(version.z.o)
                              (lib_json_c_version)
../../../../../cpu/wl82/liba/json.a(random_seed.c.o)
                              linkhash.c.o (symbol from plugin) (json_c_get_random_seed)
../../../../../cpu/wl82/liba/iperf.a(main.c.o)
                              obj/Release/apps/common/net/iperf_test.o (symbol from plugin) (iperf_main)
../../../../../cpu/wl82/liba/iperf.a(version.z.o)
                              (lib_iperf_version)
../../../../../cpu/wl82/liba/iperf.a(iperf_api.c.o)
                              main.c.o (symbol from plugin) (usage)
../../../../../cpu/wl82/liba/iperf.a(iperf_client_api.c.o)
                              main.c.o (symbol from plugin) (iperf_run_client)
../../../../../cpu/wl82/liba/iperf.a(iperf_error.c.o)
                              main.c.o (symbol from plugin) (iperf_err)
../../../../../cpu/wl82/liba/iperf.a(iperf_locale.c.o)
                              iperf_api.c.o (symbol from plugin) (usage_shortstr)
../../../../../cpu/wl82/liba/iperf.a(iperf_server_api.c.o)
                              main.c.o (symbol from plugin) (iperf_run_server)
../../../../../cpu/wl82/liba/iperf.a(iperf_tcp.c.o)
                              iperf_api.c.o (symbol from plugin) (iperf_tcp_recv)
../../../../../cpu/wl82/liba/iperf.a(iperf_udp.c.o)
                              iperf_api.c.o (symbol from plugin) (iperf_udp_recv)
../../../../../cpu/wl82/liba/iperf.a(iperf_util.c.o)
                              iperf_client_api.c.o (symbol from plugin) (make_cookie)
../../../../../cpu/wl82/liba/iperf.a(net.c.o)
                              iperf_client_api.c.o (symbol from plugin) (netdial)
../../../../../cpu/wl82/liba/iperf.a(tcp_info.c.o)
                              iperf_api.c.o (symbol from plugin) (has_tcpinfo)
../../../../../cpu/wl82/liba/iperf.a(timer.c.o)
                              iperf_api.c.o (symbol from plugin) (tmr_create)
../../../../../cpu/wl82/liba/iperf.a(units.c.o)
                              iperf_api.c.o (symbol from plugin) (unit_atof)
../../../../../cpu/wl82/liba/iperf.a(cjson.c.o)
                              iperf_api.c.o (symbol from plugin) (iperf_cJSON_Version)
../../../../../cpu/wl82/liba/iperf.a(dscp.c.o)
                              iperf_api.c.o (symbol from plugin) (parse_qos)
../../../../../cpu/wl82/liba/libmbedtls_2_2_1.a(pk.c.o)
                              https_cli.c.o (symbol from plugin) (mbedtls_pk_init)
../../../../../cpu/wl82/liba/libmbedtls_2_2_1.a(pkparse.c.o)
                              https_cli.c.o (symbol from plugin) (mbedtls_pk_parse_key)
../../../../../cpu/wl82/liba/libmbedtls_2_2_1.a(ctr_drbg.c.o)
                              https_cli.c.o (symbol from plugin) (mbedtls_ctr_drbg_init)
../../../../../cpu/wl82/liba/libmbedtls_2_2_1.a(certs.c.o)
                              https_cli.c.o (symbol from plugin) (mbedtls_test_cas_pem)
../../../../../cpu/wl82/liba/libmbedtls_2_2_1.a(pem.c.o)
                              pkparse.c.o (symbol from plugin) (mbedtls_pem_init)
../../../../../cpu/wl82/liba/libmbedtls_2_2_1.a(x509_crt.c.o)
                              https_cli.c.o (symbol from plugin) (mbedtls_x509_crt_init)
../../../../../cpu/wl82/liba/libmbedtls_2_2_1.a(entropy.c.o)
                              https_cli.c.o (symbol from plugin) (mbedtls_entropy_init)
../../../../../cpu/wl82/liba/libmbedtls_2_2_1.a(ssl_tls.c.o)
                              https_cli.c.o (symbol from plugin) (mbedtls_ssl_init)
../../../../../cpu/wl82/liba/libmbedtls_2_2_1.a(base64.c.o)
                              pem.c.o (symbol from plugin) (mbedtls_base64_decode)
../../../../../cpu/wl82/liba/libmbedtls_2_2_1.a(oid.c.o)
                              x509_crt.c.o (symbol from plugin) (mbedtls_oid_get_x509_ext_type)
../../../../../cpu/wl82/liba/libmbedtls_2_2_1.a(md.c.o)
                              pk.c.o (symbol from plugin) (mbedtls_md_info_from_type)
../../../../../cpu/wl82/liba/libmbedtls_2_2_1.a(bignum.c.o)
                              ssl_tls.c.o (symbol from plugin) (mbedtls_mpi_free)
../../../../../cpu/wl82/liba/libmbedtls_2_2_1.a(asn1parse.c.o)
                              x509_crt.c.o (symbol from plugin) (mbedtls_asn1_get_len)
../../../../../cpu/wl82/liba/libmbedtls_2_2_1.a(ssl_ciphersuites.c.o)
                              ssl_tls.c.o (symbol from plugin) (mbedtls_ssl_list_ciphersuites)
../../../../../cpu/wl82/liba/libmbedtls_2_2_1.a(x509.c.o)
                              x509_crt.c.o (symbol from plugin) (mbedtls_x509_get_serial)
../../../../../cpu/wl82/liba/libmbedtls_2_2_1.a(ssl_srv.c.o)
                              ssl_tls.c.o (symbol from plugin) (mbedtls_ssl_handshake_server_step)
../../../../../cpu/wl82/liba/libmbedtls_2_2_1.a(sha512.c.o)
                              ssl_tls.c.o (symbol from plugin) (mbedtls_sha512_init)
../../../../../cpu/wl82/liba/libmbedtls_2_2_1.a(sock_api_net.c.o)
                              https_cli.c.o (symbol from plugin) (mbedtls_net_init)
../../../../../cpu/wl82/liba/libmbedtls_2_2_1.a(version.z.o)
                              (lib_mbedtls_2_2_1_version)
../../../../../cpu/wl82/liba/libmbedtls_2_2_1.a(entropy_poll.c.o)
                              entropy.c.o (symbol from plugin) (mbedtls_platform_entropy_poll)
../../../../../cpu/wl82/liba/libmbedtls_2_2_1.a(rsa.c.o)
                              pkparse.c.o (symbol from plugin) (mbedtls_rsa_check_pubkey)
../../../../../cpu/wl82/liba/libmbedtls_2_2_1.a(pk_wrap.c.o)
                              pk.c.o (symbol from plugin) (mbedtls_rsa_info)
../../../../../cpu/wl82/liba/libmbedtls_2_2_1.a(md5.c.o)
                              pem.c.o (symbol from plugin) (mbedtls_md5_init)
../../../../../cpu/wl82/liba/libmbedtls_2_2_1.a(sha1.c.o)
                              ssl_tls.c.o (symbol from plugin) (mbedtls_sha1_init)
../../../../../cpu/wl82/liba/libmbedtls_2_2_1.a(dhm.c.o)
                              ssl_tls.c.o (symbol from plugin) (mbedtls_dhm_init)
../../../../../cpu/wl82/liba/libmbedtls_2_2_1.a(cipher.c.o)
                              ssl_tls.c.o (symbol from plugin) (mbedtls_cipher_info_from_type)
../../../../../cpu/wl82/liba/libmbedtls_2_2_1.a(ecp.c.o)
                              ssl_tls.c.o (symbol from plugin) (mbedtls_ecp_grp_id_list)
../../../../../cpu/wl82/liba/libmbedtls_2_2_1.a(ecdh.c.o)
                              ssl_tls.c.o (symbol from plugin) (mbedtls_ecdh_init)
../../../../../cpu/wl82/liba/libmbedtls_2_2_1.a(md_wrap.c.o)
                              md.c.o (symbol from plugin) (mbedtls_md5_info)
../../../../../cpu/wl82/liba/libmbedtls_2_2_1.a(ecp_curves.c.o)
                              pkparse.c.o (symbol from plugin) (mbedtls_ecp_group_load)
../../../../../cpu/wl82/liba/libmbedtls_2_2_1.a(ecdsa.c.o)
                              pk_wrap.c.o (symbol from plugin) (mbedtls_ecdsa_write_signature)
../../../../../cpu/wl82/liba/libmbedtls_2_2_1.a(ssl_cli.c.o)
                              ssl_tls.c.o (symbol from plugin) (mbedtls_ssl_handshake_client_step)
../../../../../cpu/wl82/liba/libmbedtls_2_2_1.a(aes.c.o)
                              ctr_drbg.c.o (symbol from plugin) (mbedtls_aes_init)
../../../../../cpu/wl82/liba/libmbedtls_2_2_1.a(des.c.o)
                              pem.c.o (symbol from plugin) (mbedtls_des_init)
../../../../../cpu/wl82/liba/libmbedtls_2_2_1.a(sha256.c.o)
                              ssl_tls.c.o (symbol from plugin) (mbedtls_sha256_init)
../../../../../cpu/wl82/liba/libmbedtls_2_2_1.a(cipher_wrap.c.o)
                              cipher.c.o (symbol from plugin) (mbedtls_cipher_definitions)
../../../../../cpu/wl82/liba/libmbedtls_2_2_1.a(timing.c.o)
                              entropy_poll.c.o (symbol from plugin) (mbedtls_timing_hardclock)
../../../../../cpu/wl82/liba/libmbedtls_2_2_1.a(algorithm_alt.c.o)
                              aes.c.o (symbol from plugin) (mbedtls_aes_encrypt_alt)
../../../../../cpu/wl82/liba/libmbedtls_2_2_1.a(gcm.c.o)
                              cipher_wrap.c.o (symbol from plugin) (mbedtls_gcm_init)
../../../../../cpu/wl82/liba/libmbedtls_2_2_1.a(asn1write.c.o)
                              ecdsa.c.o (symbol from plugin) (mbedtls_asn1_write_len)
../../../../../cpu/wl82/liba/lwip_2_1_3_sfc.a(dns.c.o)
                              obj/Release/apps/demo/demo_wifi/wifi_demo_task.o (symbol from plugin) (dns_getserver)
../../../../../cpu/wl82/liba/lwip_2_1_3_sfc.a(stats.c.o)
                              obj/Release/apps/demo/demo_wifi/wifi_demo_task.o (symbol from plugin) (stats_display)
../../../../../cpu/wl82/liba/lwip_2_1_3_sfc.a(memp.c.o)
                              obj/Release/apps/common/net/wifi_conf.o (symbol from plugin) (memp_get_pbuf_pool_free_cnt)
../../../../../cpu/wl82/liba/lwip_2_1_3_sfc.a(def.c.o)
                              dns.c.o (symbol from plugin) (lwip_htons)
../../../../../cpu/wl82/liba/lwip_2_1_3_sfc.a(ip4_addr.c.o)
                              dns.c.o (symbol from plugin) (ip4addr_aton)
../../../../../cpu/wl82/liba/lwip_2_1_3_sfc.a(udp.c.o)
                              dns.c.o (symbol from plugin) (udp_sendto)
../../../../../cpu/wl82/liba/lwip_2_1_3_sfc.a(netif.c.o)
                              udp.c.o (symbol from plugin) (netif_get_by_index)
../../../../../cpu/wl82/liba/lwip_2_1_3_sfc.a(pbuf.c.o)
                              dns.c.o (symbol from plugin) (pbuf_alloc)
../../../../../cpu/wl82/liba/lwip_2_1_3_sfc.a(tcp.c.o)
                              pbuf.c.o (symbol from plugin) (tcp_free_ooseq)
../../../../../cpu/wl82/liba/lwip_2_1_3_sfc.a(ip.c.o)
                              udp.c.o (symbol from plugin) (ip_data)
../../../../../cpu/wl82/liba/lwip_2_1_3_sfc.a(netdb.c.o)
                              http_cli.c.o (symbol from plugin) (lwip_gethostbyname)
../../../../../cpu/wl82/liba/lwip_2_1_3_sfc.a(tcpip.c.o)
                              dns.c.o (symbol from plugin) (tcpip_callback)
../../../../../cpu/wl82/liba/lwip_2_1_3_sfc.a(sockets.c.o)
                              netdb.c.o (symbol from plugin) (netconn_gethostbyname)
../../../../../cpu/wl82/liba/lwip_2_1_3_sfc.a(err.c.o)
                              sockets.c.o (symbol from plugin) (err_to_errno)
../../../../../cpu/wl82/liba/lwip_2_1_3_sfc.a(ethernet.c.o)
                              netif.c.o (symbol from plugin) (ethernet_input)
../../../../../cpu/wl82/liba/lwip_2_1_3_sfc.a(LwIP.c.o)
                              obj/Release/apps/demo/demo_wifi/wifi_demo_task.o (symbol from plugin) (net_set_lan_info)
../../../../../cpu/wl82/liba/lwip_2_1_3_sfc.a(sys_arch.c.o)
                              LwIP.c.o (symbol from plugin) (sys_sem_new)
../../../../../cpu/wl82/liba/lwip_2_1_3_sfc.a(wireless_ethernetif.c.o)
                              LwIP.c.o (symbol from plugin) (wireless_ethernetif_init)
../../../../../cpu/wl82/liba/lwip_2_1_3_sfc.a(sock_api.c.o)
                              sock_api_net.c.o (symbol from plugin) (sock_get_socket)
../../../../../cpu/wl82/liba/lwip_2_1_3_sfc.a(dhcp_srv.c.o)
                              obj/Release/apps/demo/demo_wifi/wifi_demo_task.o (symbol from plugin) (dhcps_get_ipaddr)
../../../../../cpu/wl82/liba/lwip_2_1_3_sfc.a(ntp.c.o)
                              LwIP.c.o (symbol from plugin) (ntp_client_get_time)
../../../../../cpu/wl82/liba/lwip_2_1_3_sfc.a(version.z.o)
                              (lib_lwip_2_1_3_version)
../../../../../cpu/wl82/liba/lwip_2_1_3_sfc.a(timeouts.c.o)
                              tcp.c.o (symbol from plugin) (tcp_timer_needed)
../../../../../cpu/wl82/liba/lwip_2_1_3_sfc.a(icmp.c.o)
                              udp.c.o (symbol from plugin) (icmp_dest_unreach)
../../../../../cpu/wl82/liba/lwip_2_1_3_sfc.a(ip4_frag.c.o)
                              timeouts.c.o (symbol from plugin) (ip_reass_tmr)
../../../../../cpu/wl82/liba/lwip_2_1_3_sfc.a(ip4.c.o)
                              udp.c.o (symbol from plugin) (ip4_route)
../../../../../cpu/wl82/liba/lwip_2_1_3_sfc.a(dhcp.c.o)
                              timeouts.c.o (symbol from plugin) (dhcp_coarse_tmr)
../../../../../cpu/wl82/liba/lwip_2_1_3_sfc.a(etharp.c.o)
                              timeouts.c.o (symbol from plugin) (etharp_tmr)
../../../../../cpu/wl82/liba/lwip_2_1_3_sfc.a(igmp.c.o)
                              netif.c.o (symbol from plugin) (igmp_start)
../../../../../cpu/wl82/liba/lwip_2_1_3_sfc.a(tcp_out.c.o)
                              sockets.c.o (symbol from plugin) (tcp_write)
../../../../../cpu/wl82/liba/lwip_2_1_3_sfc.a(init.c.o)
                              tcpip.c.o (symbol from plugin) (lwip_init)
../../../../../cpu/wl82/liba/lwip_2_1_3_sfc.a(inet_chksum.c.o)
                              udp.c.o (symbol from plugin) (ip_chksum_pseudo)
../../../../../cpu/wl82/liba/lwip_2_1_3_sfc.a(mem.c.o)
                              init.c.o (symbol from plugin) (mem_init)
../../../../../cpu/wl82/liba/lwip_2_1_3_sfc.a(tcp_in.c.o)
                              ip4.c.o (symbol from plugin) (tcp_input)
../../../../../cpu/wl82/liba/wl_wifi_sfc.a(wifi_connect.c.o)
                              obj/Release/apps/demo/demo_wifi/wifi_demo_task.o (symbol from plugin) (wifi_set_event_callback)
../../../../../cpu/wl82/liba/wl_wifi_sfc.a(ioctl.c.o)
                              driver_wext.c.o (symbol from plugin) (ioctl)
../../../../../cpu/wl82/liba/wl_wifi_sfc.a(net_device.c.o)
                              obj/Release/apps/demo/demo_wifi/wifi_demo_task.o (symbol from plugin) (wifi_get_upload_rate)
../../../../../cpu/wl82/liba/wl_wifi_sfc.a(netlink_socket.c.o)
                              main_none.c.o (symbol from plugin) (l2_packet_receive_or_netlink_receive_sem_init)
../../../../../cpu/wl82/liba/wl_wifi_sfc.a(skbuff.c.o)
                              net_device.c.o (symbol from plugin) (alloc_skb)
../../../../../cpu/wl82/liba/wl_wifi_sfc.a(spinlock.c.o)
                              skbuff.c.o (symbol from plugin) (raw_local_irq_save)
../../../../../cpu/wl82/liba/wl_wifi_sfc.a(tasklet.c.o)
                              netlink_socket.c.o (symbol from plugin) (init_timer)
../../../../../cpu/wl82/liba/wl_wifi_sfc.a(todo.c.o)
                              skbuff.c.o (symbol from plugin) (WARN_ON)
../../../../../cpu/wl82/liba/wl_wifi_sfc.a(wext-core.c.o)
                              ioctl.c.o (symbol from plugin) (wext_handle_ioctl)
../../../../../cpu/wl82/liba/wl_wifi_sfc.a(wext-priv.c.o)
                              wext-core.c.o (symbol from plugin) (iw_handler_get_private)
../../../../../cpu/wl82/liba/wl_wifi_sfc.a(wireless_main.c.o)
                              wifi_connect.c.o (symbol from plugin) (wifi_module_init)
../../../../../cpu/wl82/liba/wl_wifi_sfc.a(air_kiss.c.o)
                              obj/Release/apps/common/net/config_network.o (symbol from plugin) (wifi_airkiss_calcrc_bytes)
../../../../../cpu/wl82/liba/wl_wifi_sfc.a(simple_config.c.o)
                              net_device.c.o (symbol from plugin) (monitor_rx)
../../../../../cpu/wl82/liba/wl_wifi_sfc.a(rtmp_init.c.o)
                              wireless_main.c.o (symbol from plugin) (wl_set_OpMode)
../../../../../cpu/wl82/liba/wl_wifi_sfc.a(rtmp_timer.c.o)
                              rtmp_init.c.o (symbol from plugin) (rtmp_timer_Bss2040CoexistTimeOut)
../../../../../cpu/wl82/liba/wl_wifi_sfc.a(cmm_wpa.c.o)
                              rtmp_timer.c.o (symbol from plugin) (EnqueueStartForPSKExec)
../../../../../cpu/wl82/liba/wl_wifi_sfc.a(mlme.c.o)
                              rtmp_timer.c.o (symbol from plugin) (MlmePeriodicExec)
../../../../../cpu/wl82/liba/wl_wifi_sfc.a(ba_action.c.o)
                              rtmp_init.c.o (symbol from plugin) (BAOriSessionTearDown)
../../../../../cpu/wl82/liba/wl_wifi_sfc.a(crypt_aes.c.o)
                              cmm_wpa.c.o (symbol from plugin) (AES_Key_Wrap)
../../../../../cpu/wl82/liba/wl_wifi_sfc.a(rtusb_io.c.o)
                              wl_usb_to_sdio.c.o (symbol from plugin) (RTUSBReadMACRegister)
../../../../../cpu/wl82/liba/wl_wifi_sfc.a(cmm_data_usb.c.o)
                              rtmp_timer.c.o (symbol from plugin) (RtmpUsbStaAsicForceWakeupTimeout)
../../../../../cpu/wl82/liba/wl_wifi_sfc.a(cmm_cfg.c.o)
                              mlme.c.o (symbol from plugin) (GetPhyMode)
../../../../../cpu/wl82/liba/wl_wifi_sfc.a(crypt_hmac.c.o)
                              cmm_wpa.c.o (symbol from plugin) (RT_HMAC_SHA1)
../../../../../cpu/wl82/liba/wl_wifi_sfc.a(cmm_sanity.c.o)
                              ba_action.c.o (symbol from plugin) (PeerAddBAReqActionSanity)
../../../../../cpu/wl82/liba/wl_wifi_sfc.a(cmm_aes.c.o)
                              cmm_wpa.c.o (symbol from plugin) (RTMPConstructCCMPHdr)
../../../../../cpu/wl82/liba/wl_wifi_sfc.a(cmm_mac_usb.c.o)
                              rtmp_timer.c.o (symbol from plugin) (BeaconUpdateExec)
../../../../../cpu/wl82/liba/wl_wifi_sfc.a(cmm_asic.c.o)
                              mlme.c.o (symbol from plugin) (AsicUpdateProtect)
../../../../../cpu/wl82/liba/wl_wifi_sfc.a(cmm_data.c.o)
                              cmm_wpa.c.o (symbol from plugin) (MiniportMMRequest)
../../../../../cpu/wl82/liba/wl_wifi_sfc.a(action.c.o)
                              mlme.c.o (symbol from plugin) (ActionStateMachineInit)
../../../../../cpu/wl82/liba/wl_wifi_sfc.a(cmm_info.c.o)
                              rtmp_init.c.o (symbol from plugin) (Set_WirelessMode_Proc)
../../../../../cpu/wl82/liba/wl_wifi_sfc.a(cmm_tkip.c.o)
                              cmm_wpa.c.o (symbol from plugin) (RTMPSoftDecryptTKIP)
../../../../../cpu/wl82/liba/wl_wifi_sfc.a(dfs.c.o)
                              mlme.c.o (symbol from plugin) (RadarChannelCheck)
../../../../../cpu/wl82/liba/wl_wifi_sfc.a(rt_profile.c.o)
                              cmm_wpa.c.o (symbol from plugin) (RTMP_IndicateMediaState)
../../../../../cpu/wl82/liba/wl_wifi_sfc.a(rt_main_dev.c.o)
                              net_device.c.o (symbol from plugin) (MainVirtualIF_close)
../../../../../cpu/wl82/liba/wl_wifi_sfc.a(usb_main_dev.c.o)
                              wl_usb_to_sdio.c.o (symbol from plugin) (rtusb_probe)
../../../../../cpu/wl82/liba/wl_wifi_sfc.a(sta_ioctl.c.o)
                              rt_main_dev.c.o (symbol from plugin) (rt28xx_iw_handler_def)
../../../../../cpu/wl82/liba/wl_wifi_sfc.a(connect.c.o)
                              mlme.c.o (symbol from plugin) (MlmeCntlInit)
../../../../../cpu/wl82/liba/wl_wifi_sfc.a(auth_rsp.c.o)
                              mlme.c.o (symbol from plugin) (AuthRspStateMachineInit)
../../../../../cpu/wl82/liba/wl_wifi_sfc.a(sanity.c.o)
                              cmm_sanity.c.o (symbol from plugin) (GetTimBit)
../../../../../cpu/wl82/liba/wl_wifi_sfc.a(wpa.c.o)
                              cmm_data_usb.c.o (symbol from plugin) (RTMPReportMicError)
../../../../../cpu/wl82/liba/wl_wifi_sfc.a(assoc.c.o)
                              mlme.c.o (symbol from plugin) (AssocStateMachineInit)
../../../../../cpu/wl82/liba/wl_wifi_sfc.a(sync.c.o)
                              mlme.c.o (symbol from plugin) (SyncStateMachineInit)
../../../../../cpu/wl82/liba/wl_wifi_sfc.a(sta_cfg.c.o)
                              cmm_cfg.c.o (symbol from plugin) (StaSiteSurvey)
../../../../../cpu/wl82/liba/wl_wifi_sfc.a(auth.c.o)
                              mlme.c.o (symbol from plugin) (AuthStateMachineInit)
../../../../../cpu/wl82/liba/wl_wifi_sfc.a(rtmp_data.c.o)
                              cmm_data.c.o (symbol from plugin) (STARxEAPOLFrameIndicate)
../../../../../cpu/wl82/liba/wl_wifi_sfc.a(ap_auth.c.o)
                              mlme.c.o (symbol from plugin) (APAuthStateMachineInit)
../../../../../cpu/wl82/liba/wl_wifi_sfc.a(ap_connect.c.o)
                              cmm_mac_usb.c.o (symbol from plugin) (BeaconTransmitRequired)
../../../../../cpu/wl82/liba/wl_wifi_sfc.a(ap_sync.c.o)
                              mlme.c.o (symbol from plugin) (APSyncStateMachineInit)
../../../../../cpu/wl82/liba/wl_wifi_sfc.a(ap_sanity.c.o)
                              ap_auth.c.o (symbol from plugin) (PeerDeauthReqSanity)
../../../../../cpu/wl82/liba/wl_wifi_sfc.a(ap_mlme.c.o)
                              rtmp_timer.c.o (symbol from plugin) (Bss2040CoexistTimeOut)
../../../../../cpu/wl82/liba/wl_wifi_sfc.a(ap_wpa.c.o)
                              rtusb_io.c.o (symbol from plugin) (HandleCounterMeasure)
../../../../../cpu/wl82/liba/wl_wifi_sfc.a(ap_assoc.c.o)
                              mlme.c.o (symbol from plugin) (APAssocStateMachineInit)
../../../../../cpu/wl82/liba/wl_wifi_sfc.a(ap.c.o)
                              rtusb_io.c.o (symbol from plugin) (APStartUp)
../../../../../cpu/wl82/liba/wl_wifi_sfc.a(ap_data.c.o)
                              rt_profile.c.o (symbol from plugin) (APSendPackets)
../../../../../cpu/wl82/liba/wl_wifi_sfc.a(ap_autoChSel.c.o)
                              ap_sync.c.o (symbol from plugin) (AutoChBssSearchWithSSID)
../../../../../cpu/wl82/liba/wl_wifi_sfc.a(p2p_cfg.c.o)
                              rt_main_dev.c.o (symbol from plugin) (RTMP_AP_IoctlHandle)
../../../../../cpu/wl82/liba/wl_wifi_sfc.a(ap_ioctl.c.o)
                              rt_main_dev.c.o (symbol from plugin) (rt28xx_ap_ioctl)
../../../../../cpu/wl82/liba/wl_wifi_sfc.a(version.z.o)
                              (lib_wifi_version)
../../../../../cpu/wl82/liba/wl_wifi_sfc.a(wl_mp_test.c.o)
                              simple_config.c.o (symbol from plugin) (wl_scan)
../../../../../cpu/wl82/liba/wl_wifi_sfc.a(jl_todo.c.o)
                              cmm_cfg.c.o (symbol from plugin) (isxdigit)
../../../../../cpu/wl82/liba/wl_wifi_sfc.a(os_priv.c.o)
                              tasklet.c.o (symbol from plugin) (kthread_set_stop)
../../../../../cpu/wl82/liba/wl_wifi_sfc.a(rt_os_util.c.o)
                              cmm_cfg.c.o (symbol from plugin) (RtmpDrvMaxRateGet)
../../../../../cpu/wl82/liba/wl_wifi_sfc.a(rt_linux.c.o)
                              rtmp_init.c.o (symbol from plugin) (RtmpUtilInit)
../../../../../cpu/wl82/liba/wl_wifi_sfc.a(rt30xx.c.o)
                              mlme.c.o (symbol from plugin) (RT30xxHaltAction)
../../../../../cpu/wl82/liba/wl_wifi_sfc.a(rtmp_chip.c.o)
                              rtmp_init.c.o (symbol from plugin) (RtmpChipOpsHook)
../../../../../cpu/wl82/liba/wl_wifi_sfc.a(spectrum.c.o)
                              cmm_info.c.o (symbol from plugin) (NotifyChSwAnnToPeerAPs)
../../../../../cpu/wl82/liba/wl_wifi_sfc.a(rtusb_bulk.c.o)
                              rtusb_io.c.o (symbol from plugin) (RTUSBInitHTTxDesc)
../../../../../cpu/wl82/liba/wl_wifi_sfc.a(cmm_sync.c.o)
                              cmm_info.c.o (symbol from plugin) (BuildChannelList)
../../../../../cpu/wl82/liba/wl_wifi_sfc.a(cmm_profile.c.o)
                              cmm_info.c.o (symbol from plugin) (rstrtok)
../../../../../cpu/wl82/liba/wl_wifi_sfc.a(crypt_md5.c.o)
                              crypt_hmac.c.o (symbol from plugin) (RT_MD5_Init)
../../../../../cpu/wl82/liba/wl_wifi_sfc.a(rtusb_data.c.o)
                              cmm_data.c.o (symbol from plugin) (RTUSBFreeDescriptorRequest)
../../../../../cpu/wl82/liba/wl_wifi_sfc.a(cmm_cmd.c.o)
                              cmm_mac_usb.c.o (symbol from plugin) (RTInitializeCmdQ)
../../../../../cpu/wl82/liba/wl_wifi_sfc.a(cmm_wep.c.o)
                              cmm_tkip.c.o (symbol from plugin) (RTMP_CALC_FCS32)
../../../../../cpu/wl82/liba/wl_wifi_sfc.a(rt_channel.c.o)
                              cmm_sync.c.o (symbol from plugin) (TotalChNum)
../../../../../cpu/wl82/liba/wl_wifi_sfc.a(rtmp_init_inf.c.o)
                              rt_main_dev.c.o (symbol from plugin) (rt28xx_init)
../../../../../cpu/wl82/liba/wl_wifi_sfc.a(crypt_arc4.c.o)
                              cmm_tkip.c.o (symbol from plugin) (ARC4_INIT)
../../../../../cpu/wl82/liba/wl_wifi_sfc.a(crypt_sha2.c.o)
                              crypt_hmac.c.o (symbol from plugin) (RT_SHA1_Init)
../../../../../cpu/wl82/liba/wl_wifi_sfc.a(rt_usb.c.o)
                              rtmp_init_inf.c.o (symbol from plugin) (RtmpMgmtTaskInit)
../../../../../cpu/wl82/liba/wl_wifi_sfc.a(wait.c.o)
                              rt_linux.c.o (symbol from plugin) (init_waitqueue_head)
../../../../../cpu/wl82/liba/wl_wifi_sfc.a(rt_usb_util.c.o)
                              rtmp_init_inf.c.o (symbol from plugin) (RtmpOsUsbEmptyUrbCheck)
../../../../../cpu/wl82/liba/cpu.a(sd.c.o)
                              wl_sdio.c.o (symbol from plugin) (sdio_host_send_command)
../../../../../cpu/wl82/liba/cpu.a(wlc.c.o)
                              wl_usb_to_sdio.c.o (symbol from plugin) (wlc_main)
../../../../../cpu/wl82/liba/cpu.a(aes_hw.c.o)
                              algorithm_alt.c.o (symbol from plugin) (jl_aes_encrypt_hw)
../../../../../cpu/wl82/liba/cpu.a(sha_hw.c.o)
                              algorithm_alt.c.o (symbol from plugin) (jl_sha1_process)
../../../../../cpu/wl82/liba/common_lib.a(rand.c.o)
                              iperf_util.c.o (symbol from plugin) (random32)
../../../../../cpu/wl82/liba/common_lib.a(vfprintf.c.o)
                              debug.c.o (symbol from plugin) (vfprintf)
../../../../../cpu/wl82/liba/common_lib.a(strftime.c.o)
                              iperf_api.c.o (symbol from plugin) (strftime)
../../../../../cpu/wl82/liba/common_lib.a(getopt.c.o)
                              iperf_api.c.o (symbol from plugin) (getopt_long)
../../../../../cpu/wl82/liba/common_lib.a(assert.c.o)
                              net.c.o (symbol from plugin) (__assert_func_cmpt)
../../../../../cpu/wl82/liba/common_lib.a(cmpt_todo.c.o)
                              driver_wext.c.o (symbol from plugin) (if_nametoindex)
../../../../../cpu/wl82/liba/hsm.a(hsm.c.o)
                              wifi_connect.c.o (symbol from plugin) (HSM_STATE_Create)
../../../../../include_lib/newlib/pi32v2-lib/libm.a(lib_a-s_fpclassify.o)
                              json_object.c.o (symbol from plugin) (__fpclassifyd)
../../../../../include_lib/newlib/pi32v2-lib/libm.a(lib_a-s_modf.o)
                              fcvt.c.o (symbol from plugin) (modf)
../../../../../include_lib/newlib/pi32v2-lib/libc.a(lib_a-atoi.o)
                              ieee802_11_common.c.o (symbol from plugin) (atoi)
../../../../../include_lib/newlib/pi32v2-lib/libc.a(lib_a-bsearch.o)
                              arraylist.c.o (symbol from plugin) (bsearch)
../../../../../include_lib/newlib/pi32v2-lib/libc.a(lib_a-ctype_.o)
                              string.c.o (symbol from plugin) (__ctype_ptr__)
../../../../../include_lib/newlib/pi32v2-lib/libc.a(lib_a-gmtime_r.o)
                              iperf_api.c.o (symbol from plugin) (gmtime_r)
../../../../../include_lib/newlib/pi32v2-lib/libc.a(lib_a-impure.o)
                              debug.c.o (symbol from plugin) (_impure_ptr)
../../../../../include_lib/newlib/pi32v2-lib/libc.a(lib_a-islower.o)
                              cmm_profile.c.o (symbol from plugin) (islower)
../../../../../include_lib/newlib/pi32v2-lib/libc.a(lib_a-isspace.o)
                              cmm_profile.c.o (symbol from plugin) (isspace)
../../../../../include_lib/newlib/pi32v2-lib/libc.a(lib_a-locale.o)
                              cjson.c.o (symbol from plugin) (localeconv)
../../../../../include_lib/newlib/pi32v2-lib/libc.a(lib_a-memset.o)
                              common.c.o (symbol from plugin) (memset)
../../../../../include_lib/newlib/pi32v2-lib/libc.a(lib_a-qsort.o)
                              scan.c.o (symbol from plugin) (qsort)
../../../../../include_lib/newlib/pi32v2-lib/libc.a(lib_a-rand.o)
                              os_none.c.o (symbol from plugin) (rand)
../../../../../include_lib/newlib/pi32v2-lib/libc.a(lib_a-strcasecmp.o)
                              os_none.c.o (symbol from plugin) (strcasecmp)
../../../../../include_lib/newlib/pi32v2-lib/libc.a(lib_a-strcat.o)
                              ff_opr.c.o (symbol from plugin) (strcat)
../../../../../include_lib/newlib/pi32v2-lib/libc.a(lib_a-strchr.o)
                              sdfile_new.c.o (symbol from plugin) (strchr)
../../../../../include_lib/newlib/pi32v2-lib/libc.a(lib_a-strncasecmp.o)
                              os_none.c.o (symbol from plugin) (strncasecmp)
../../../../../include_lib/newlib/pi32v2-lib/libc.a(lib_a-strncat.o)
                              iperf_api.c.o (symbol from plugin) (strncat)
../../../../../include_lib/newlib/pi32v2-lib/libc.a(lib_a-strpbrk.o)
                              http_cli.c.o (symbol from plugin) (strpbrk)
../../../../../include_lib/newlib/pi32v2-lib/libc.a(lib_a-strrchr.o)
                              sdfile_new.c.o (symbol from plugin) (strrchr)
../../../../../include_lib/newlib/pi32v2-lib/libc.a(lib_a-strsep.o)
                              p2p_cfg.c.o (symbol from plugin) (strsep)
../../../../../include_lib/newlib/pi32v2-lib/libc.a(lib_a-strspn.o)
                              driver_wext.c.o (symbol from plugin) (strspn)
../../../../../include_lib/newlib/pi32v2-lib/libc.a(lib_a-strtok_r.o)
                              ../../../../../include_lib/newlib/pi32v2-lib/libc.a(lib_a-strsep.o) (__strtok_r)
../../../../../include_lib/newlib/pi32v2-lib/libc.a(lib_a-strtoll.o)
                              json_util.c.o (symbol from plugin) (strtoll)
../../../../../include_lib/newlib/pi32v2-lib/libc.a(lib_a-strtoll_r.o)
                              ../../../../../include_lib/newlib/pi32v2-lib/libc.a(lib_a-strtoll.o) (_strtoll_r)
../../../../../include_lib/newlib/pi32v2-lib/libc.a(lib_a-strtol.o)
                              ../../../../../include_lib/newlib/pi32v2-lib/libc.a(lib_a-atoi.o) (_strtol_r)
../../../../../include_lib/newlib/pi32v2-lib/libc.a(lib_a-strtoul.o)
                              rtc.c.o (symbol from plugin) (strtoul)
../../../../../include_lib/newlib/pi32v2-lib/libc.a(lib_a-tolower.o)
                              todo.c.o (symbol from plugin) (tolower)
../../../../../include_lib/newlib/pi32v2-lib/libc.a(lib_a-toupper.o)
                              p2p_cfg.c.o (symbol from plugin) (toupper)
../../../../../include_lib/newlib/pi32v2-lib/libcompiler_rt.a(subdf3.o)
                              ../../../../../include_lib/newlib/pi32v2-lib/libm.a(lib_a-s_modf.o) (__subdf3)
../../../../../include_lib/newlib/pi32v2-lib/libcompiler_rt.a(udivdi3.o)
                              ../../../../../include_lib/newlib/pi32v2-lib/libc.a(lib_a-strtoll_r.o) (__udivdi3)
../../../../../include_lib/newlib/pi32v2-lib/libcompiler_rt.a(udivmoddi4.o)
                              ../../../../../include_lib/newlib/pi32v2-lib/libcompiler_rt.a(udivdi3.o) (__udivmoddi4)
../../../../../include_lib/newlib/pi32v2-lib/libcompiler_rt.a(umoddi3.o)
                              ../../../../../include_lib/newlib/pi32v2-lib/libc.a(lib_a-strtoll_r.o) (__umoddi3)
../../../../../include_lib/newlib/pi32v2-lib/libcompiler_rt.a(adddf3.o)
                              ../../../../../include_lib/newlib/pi32v2-lib/libcompiler_rt.a(subdf3.o) (__adddf3)
../../../../../include_lib/newlib/pi32v2-lib/libc.a(lib_a-memchr.o)
                              ..\..\..\..\..\cpu\wl82\tools\sdk.elf.o (memchr)
../../../../../include_lib/newlib/pi32v2-lib/libc.a(lib_a-memcpy.o)
                              ..\..\..\..\..\cpu\wl82\tools\sdk.elf.o (memcpy)
../../../../../include_lib/newlib/pi32v2-lib/libcompiler_rt.a(comparedf2.o)
                              ..\..\..\..\..\cpu\wl82\tools\sdk.elf.o (__eqdf2)
../../../../../include_lib/newlib/pi32v2-lib/libcompiler_rt.a(divdf3.o)
                              ..\..\..\..\..\cpu\wl82\tools\sdk.elf.o (__divdf3)
../../../../../include_lib/newlib/pi32v2-lib/libcompiler_rt.a(extendsfdf2.o)
                              ..\..\..\..\..\cpu\wl82\tools\sdk.elf.o (__extendsfdf2)
../../../../../include_lib/newlib/pi32v2-lib/libcompiler_rt.a(fixdfdi.o)
                              ..\..\..\..\..\cpu\wl82\tools\sdk.elf.o (__fixdfdi)
../../../../../include_lib/newlib/pi32v2-lib/libcompiler_rt.a(fixdfsi.o)
                              ..\..\..\..\..\cpu\wl82\tools\sdk.elf.o (__fixdfsi)
../../../../../include_lib/newlib/pi32v2-lib/libcompiler_rt.a(fixunsdfdi.o)
                              ..\..\..\..\..\cpu\wl82\tools\sdk.elf.o (__fixunsdfdi)
../../../../../include_lib/newlib/pi32v2-lib/libcompiler_rt.a(fixunsdfsi.o)
                              ../../../../../include_lib/newlib/pi32v2-lib/libcompiler_rt.a(fixunsdfdi.o) (__fixunsdfsi)
../../../../../include_lib/newlib/pi32v2-lib/libcompiler_rt.a(floatdidf.o)
                              ..\..\..\..\..\cpu\wl82\tools\sdk.elf.o (__floatdidf)
../../../../../include_lib/newlib/pi32v2-lib/libcompiler_rt.a(floatsidf.o)
                              ..\..\..\..\..\cpu\wl82\tools\sdk.elf.o (__floatsidf)
../../../../../include_lib/newlib/pi32v2-lib/libcompiler_rt.a(floatundidf.o)
                              ..\..\..\..\..\cpu\wl82\tools\sdk.elf.o (__floatundidf)
../../../../../include_lib/newlib/pi32v2-lib/libcompiler_rt.a(floatunsidf.o)
                              ..\..\..\..\..\cpu\wl82\tools\sdk.elf.o (__floatunsidf)
../../../../../include_lib/newlib/pi32v2-lib/libcompiler_rt.a(muldf3.o)
                              ..\..\..\..\..\cpu\wl82\tools\sdk.elf.o (__muldf3)
../../../../../include_lib/newlib/pi32v2-lib/libcompiler_rt.a(truncdfsf2.o)
                              ..\..\..\..\..\cpu\wl82\tools\sdk.elf.o (__truncdfsf2)

Discarded input sections

 .text          0x00000000        0x0 obj/Release/cpu/wl82/debug.o (symbol from plugin)
 .text          0x00000000        0x0 obj/Release/cpu/wl82/iic.o (symbol from plugin)
 .text          0x00000000        0x0 obj/Release/cpu/wl82/key/adc_api.o (symbol from plugin)
 .text          0x00000000        0x0 obj/Release/cpu/wl82/key/ctmu.o (symbol from plugin)
 .text          0x00000000        0x0 obj/Release/cpu/wl82/key/irflt.o (symbol from plugin)
 .text          0x00000000        0x0 obj/Release/cpu/wl82/key/plcnt.o (symbol from plugin)
 .text          0x00000000        0x0 obj/Release/cpu/wl82/key/rdec.o (symbol from plugin)
 .text          0x00000000        0x0 obj/Release/cpu/wl82/port_waked_up.o (symbol from plugin)
 .text          0x00000000        0x0 obj/Release/cpu/wl82/setup.o (symbol from plugin)
 .text          0x00000000        0x0 obj/Release/apps/common/config/log_config/lib_driver_config.o (symbol from plugin)
 .text          0x00000000        0x0 obj/Release/apps/common/config/log_config/lib_system_config.o (symbol from plugin)
 .text          0x00000000        0x0 obj/Release/apps/common/config/log_config/lib_update_config.o (symbol from plugin)
 .text          0x00000000        0x0 obj/Release/apps/common/config/user_cfg.o (symbol from plugin)
 .text          0x00000000        0x0 obj/Release/apps/common/debug/debug.o (symbol from plugin)
 .text          0x00000000        0x0 obj/Release/apps/common/debug/debug_user.o (symbol from plugin)
 .text          0x00000000        0x0 obj/Release/apps/common/iic/iic.o (symbol from plugin)
 .text          0x00000000        0x0 obj/Release/apps/common/iic/software_iic.o (symbol from plugin)
 .text          0x00000000        0x0 obj/Release/apps/common/net/assign_macaddr.o (symbol from plugin)
 .text          0x00000000        0x0 obj/Release/apps/common/net/config_network.o (symbol from plugin)
 .text          0x00000000        0x0 obj/Release/apps/common/net/iperf_test.o (symbol from plugin)
 .text          0x00000000        0x0 obj/Release/apps/common/net/platform_cfg.o (symbol from plugin)
 .text          0x00000000        0x0 obj/Release/apps/common/net/wifi_conf.o (symbol from plugin)
 .text          0x00000000        0x0 obj/Release/apps/common/rf_fcc_tool/bt.o (symbol from plugin)
 .text          0x00000000        0x0 obj/Release/apps/common/rf_fcc_tool/rf_fcc_main.o (symbol from plugin)
 .text          0x00000000        0x0 obj/Release/apps/common/system/init.o (symbol from plugin)
 .text          0x00000000        0x0 obj/Release/apps/common/system/system_reset_reason.o (symbol from plugin)
 .text          0x00000000        0x0 obj/Release/apps/common/system/version.o (symbol from plugin)
 .text          0x00000000        0x0 obj/Release/apps/common/update/update.o (symbol from plugin)
 .text          0x00000000        0x0 obj/Release/apps/demo/demo_wifi/app_main.o (symbol from plugin)
 .text          0x00000000        0x0 obj/Release/apps/demo/demo_wifi/epd_touch/epd/epd_display.o (symbol from plugin)
 .text          0x00000000        0x0 obj/Release/apps/demo/demo_wifi/epd_touch/epd/epd_display_new.o (symbol from plugin)
 .text          0x00000000        0x0 obj/Release/apps/demo/demo_wifi/epd_touch/epd/epd_partial_s37t03.o (symbol from plugin)
 .text          0x00000000        0x0 obj/Release/apps/demo/demo_wifi/epd_touch/epd/epd_predefined_data.o (symbol from plugin)
 .text          0x00000000        0x0 obj/Release/apps/demo/demo_wifi/epd_touch/epd/epd_spi.o (symbol from plugin)
 .text          0x00000000        0x0 obj/Release/apps/demo/demo_wifi/epd_touch/epd/epd_unified.o (symbol from plugin)
 .text          0x00000000        0x0 obj/Release/apps/demo/demo_wifi/epd_touch/epd_touch_api.o (symbol from plugin)
 .text          0x00000000        0x0 obj/Release/apps/demo/demo_wifi/epd_touch/examples/epd_touch_advanced_example.o (symbol from plugin)
 .text          0x00000000        0x0 obj/Release/apps/demo/demo_wifi/epd_touch/examples/epd_touch_example.o (symbol from plugin)
 .text          0x00000000        0x0 obj/Release/apps/demo/demo_wifi/epd_touch/fonts/font12.o (symbol from plugin)
 .text          0x00000000        0x0 obj/Release/apps/demo/demo_wifi/epd_touch/fonts/font12CN.o (symbol from plugin)
 .text          0x00000000        0x0 obj/Release/apps/demo/demo_wifi/epd_touch/fonts/font16.o (symbol from plugin)
 .text          0x00000000        0x0 obj/Release/apps/demo/demo_wifi/epd_touch/fonts/font20.o (symbol from plugin)
 .text          0x00000000        0x0 obj/Release/apps/demo/demo_wifi/epd_touch/fonts/font24.o (symbol from plugin)
 .text          0x00000000        0x0 obj/Release/apps/demo/demo_wifi/epd_touch/fonts/font24CN.o (symbol from plugin)
 .text          0x00000000        0x0 obj/Release/apps/demo/demo_wifi/epd_touch/fonts/font8.o (symbol from plugin)
 .text          0x00000000        0x0 obj/Release/apps/demo/demo_wifi/epd_touch/gui/gui_paint.o (symbol from plugin)
 .text          0x00000000        0x0 obj/Release/apps/demo/demo_wifi/epd_touch/hardware/exti.o (symbol from plugin)
 .text          0x00000000        0x0 obj/Release/apps/demo/demo_wifi/epd_touch/hardware/hardware_config.o (symbol from plugin)
 .text          0x00000000        0x0 obj/Release/apps/demo/demo_wifi/epd_touch/hardware/key.o (symbol from plugin)
 .text          0x00000000        0x0 obj/Release/apps/demo/demo_wifi/epd_touch/hardware/led.o (symbol from plugin)
 .text          0x00000000        0x0 obj/Release/apps/demo/demo_wifi/epd_touch/hardware/power_mgmt.o (symbol from plugin)
 .text          0x00000000        0x0 obj/Release/apps/demo/demo_wifi/epd_touch/hardware/system_check.o (symbol from plugin)
 .text          0x00000000        0x0 obj/Release/apps/demo/demo_wifi/epd_touch/test/epd_touch_test.o (symbol from plugin)
 .text          0x00000000        0x0 obj/Release/apps/demo/demo_wifi/epd_touch/touch/cst836u.o (symbol from plugin)
 .text          0x00000000        0x0 obj/Release/apps/demo/demo_wifi/llm_chat.o (symbol from plugin)
 .text          0x00000000        0x0 obj/Release/apps/demo/demo_wifi/llm_chat_ui.o (symbol from plugin)
 .text          0x00000000        0x0 obj/Release/apps/demo/demo_wifi/llm_http.o (symbol from plugin)
 .text          0x00000000        0x0 obj/Release/apps/demo/demo_wifi/llm_json.o (symbol from plugin)
 .text          0x00000000        0x0 obj/Release/apps/demo/demo_wifi/wifi_demo_task.o (symbol from plugin)
 .text          0x00000000        0x0 obj/Release/apps/demo/demo_wifi/board/wl82/board.o (symbol from plugin)
 .text          0x00000000        0x0 uart.c.o (symbol from plugin)
 .text          0x00000000        0x0 delay.c.o (symbol from plugin)
 .text          0x00000000        0x0 wdt.c.o (symbol from plugin)
 .text          0x00000000        0x0 crc16.c.o (symbol from plugin)
 .text          0x00000000        0x0 clock.c.o (symbol from plugin)
 .text          0x00000000        0x0 gpio.c.o (symbol from plugin)
 .text          0x00000000        0x0 rtc.c.o (symbol from plugin)
 .text          0x00000000        0x0 usb.c.o (symbol from plugin)
 .text          0x00000000        0x0 timer.c.o (symbol from plugin)
 .text          0x00000000        0x0 spec_uart.c.o (symbol from plugin)
 .text          0x00000000        0x0 spi0.c.o (symbol from plugin)
 .text          0x00000000        0x0 lrc_hw.c.o (symbol from plugin)
 .text          0x00000000        0x0 p33.c.o (symbol from plugin)
 .text          0x00000000        0x0 power_hw.c.o (symbol from plugin)
 .text          0x00000000        0x0 power_wakeup.c.o (symbol from plugin)
 .text          0x00000000        0x0 dac.c.o (symbol from plugin)
 .text          0x00000000        0x0 vm_sfc.c.o (symbol from plugin)
 .text          0x00000000        0x0 otg.c.o (symbol from plugin)
 .text          0x00000000        0x0 usb_phy.c.o (symbol from plugin)
 .text          0x00000000        0x0 device_api.c.o (symbol from plugin)
 .text          0x00000000        0x0 boot.c.o (symbol from plugin)
 .text          0x00000000        0x0 cache.c.o (symbol from plugin)
 .text          0x00000000        0x0 encryption.c.o (symbol from plugin)
 .text          0x00000000        0x0 lp_timer_hw.c.o (symbol from plugin)
 .text          0x00000000        0x0 key0_decode.c.o (symbol from plugin)
 .text          0x00000000        0x0 sdfile_new.c.o (symbol from plugin)
 .text          0x00000000        0x0 sdfile_ext.c.o (symbol from plugin)
 .text          0x00000000        0x0 vfs.c.o (symbol from plugin)
 .text          0x00000000        0x0 fat_compact.c.o (symbol from plugin)
 .text          0x00000000        0x0 UTF8Unicode_conversion.c.o (symbol from plugin)
 .text          0x00000000        0x0 mbr.c.o (symbol from plugin)
 .text          0x00000000        0x0 tff.c.o (symbol from plugin)
 .text          0x00000000        0x0 ffunicode.c.o (symbol from plugin)
 .text          0x00000000        0x0 ff_opr.c.o (symbol from plugin)
 .text          0x00000000        0x0 net_event.c.o (symbol from plugin)
 .text          0x00000000        0x0 device_event.c.o (symbol from plugin)
 .text          0x00000000        0x0 event.c.o (symbol from plugin)
 .text          0x00000000        0x0 app_core.c.o (symbol from plugin)
 .text          0x00000000        0x0 ASCII_lib.c.o (symbol from plugin)
 .text          0x00000000        0x0 circular_buf.c.o (symbol from plugin)
 .text          0x00000000        0x0 hwi.c.o (symbol from plugin)
 .text          0x00000000        0x0 port.c.o (symbol from plugin)
 .text          0x00000000        0x0 movable.c.o (symbol from plugin)
 .text          0x00000000        0x0 tasks.c.o (symbol from plugin)
 .text          0x00000000        0x0 main.c.o (symbol from plugin)
 .text          0x00000000        0x0 task_status.c.o (symbol from plugin)
 .text          0x00000000        0x0 os_api.c.o (symbol from plugin)
 .text          0x00000000        0x0 malloc.c.o (symbol from plugin)
 .text          0x00000000        0x0 printf-stdarg.c.o (symbol from plugin)
 .text          0x00000000        0x0 puthex.c.o (symbol from plugin)
 .text          0x00000000        0x0 log.c.o (symbol from plugin)
 .text          0x00000000        0x0 task.c.o (symbol from plugin)
 .text          0x00000000        0x0 timer.c.o (symbol from plugin)
 .text          0x00000000        0x0 wait_completion.c.o (symbol from plugin)
 .text          0x00000000        0x0 sys_timer_task.c.o (symbol from plugin)
 .text          0x00000000        0x0 port_overwrite.c.o (symbol from plugin)
 .text          0x00000000        0x0 list.c.o (symbol from plugin)
 .text          0x00000000        0x0 queue.c.o (symbol from plugin)
 .text          0x00000000        0x0 event_groups.c.o (symbol from plugin)
 .text          0x00000000        0x0 lbuf.c.o (symbol from plugin)
 .text          0x00000000        0x0 fcvt.c.o (symbol from plugin)
 .text          0x00000000        0x0 vm.c.o (symbol from plugin)
 .text          0x00000000        0x0 syscfg_api.c.o (symbol from plugin)
 .text          0x00000000        0x0 cfg_bin.c.o (symbol from plugin)
 .text          0x00000000        0x0 cfg_btif.c.o (symbol from plugin)
 .text          0x00000000        0x0 time.c.o (symbol from plugin)
 .text          0x00000000        0x0 string.c.o (symbol from plugin)
 .text          0x00000000        0x0 weak.c.o (symbol from plugin)
 .text          0x00000000        0x0 Vsprintf.c.o (symbol from plugin)
 .text          0x00000000        0x0 mktime.c.o (symbol from plugin)
 .text          0x00000000        0x0 character_encoding.c.o (symbol from plugin)
 .text          0x00000000        0x0 strtod.c.o (symbol from plugin)
 .text          0x00000000        0x0 update_main.c.o (symbol from plugin)
 .text          0x00000000        0x0 flash_fs_api.c.o (symbol from plugin)
 .text          0x00000000        0x0 dual_bank_passive_update.c.o (symbol from plugin)
 .text          0x00000000        0x0 reserved_zone_update.c.o (symbol from plugin)
 .text          0x00000000        0x0 dev_upgrade_api.c.o (symbol from plugin)
 .text          0x00000000        0x0 encrypt_api.c.o (symbol from plugin)
 .text          0x00000000        0x0 download_loop.c.o (symbol from plugin)
 .text          0x00000000        0x0 wf_deep_sleep.c.o (symbol from plugin)
 .text          0x00000000        0x0 wl_sdio.c.o (symbol from plugin)
 .text          0x00000000        0x0 wl_hw_init.c.o (symbol from plugin)
 .text          0x00000000        0x0 wl_usb_to_sdio.c.o (symbol from plugin)
 .text          0x00000000        0x0 wl_sdio_host.c.o (symbol from plugin)
 .text          0x00000000        0x0 wf_phy_mac_lib.c.o (symbol from plugin)
 .text          0x00000000        0x0 wf_phy_mac_init.c.o (symbol from plugin)
 .text          0x00000000        0x0 wf_cfr_init.c.o (symbol from plugin)
 .text          0x00000000        0x0 wf_rf_init.c.o (symbol from plugin)
 .text          0x00000000        0x0 wf_rf_cal.c.o (symbol from plugin)
 .text          0x00000000        0x0 wf_soft_adj.c.o (symbol from plugin)
 .text          0x00000000        0x0 wf_pa_dpd.c.o (symbol from plugin)
 .text          0x00000000        0x0 wl_rfd_init.c.o (symbol from plugin)
 .text          0x00000000        0x0 main_none.c.o (symbol from plugin)
 .text          0x00000000        0x0 scan.c.o (symbol from plugin)
 .text          0x00000000        0x0 wpa_supplicant.c.o (symbol from plugin)
 .text          0x00000000        0x0 wpas_glue.c.o (symbol from plugin)
 .text          0x00000000        0x0 sha1-pbkdf2.c.o (symbol from plugin)
 .text          0x00000000        0x0 sha1.c.o (symbol from plugin)
 .text          0x00000000        0x0 hw_features_common.c.o (symbol from plugin)
 .text          0x00000000        0x0 ieee802_11_common.c.o (symbol from plugin)
 .text          0x00000000        0x0 wpa_common.c.o (symbol from plugin)
 .text          0x00000000        0x0 md5.c.o (symbol from plugin)
 .text          0x00000000        0x0 sha1-internal.c.o (symbol from plugin)
 .text          0x00000000        0x0 sha1-prf.c.o (symbol from plugin)
 .text          0x00000000        0x0 driver_common.c.o (symbol from plugin)
 .text          0x00000000        0x0 drivers.c.o (symbol from plugin)
 .text          0x00000000        0x0 l2_packet_none.c.o (symbol from plugin)
 .text          0x00000000        0x0 wpa.c.o (symbol from plugin)
 .text          0x00000000        0x0 wpa_ie.c.o (symbol from plugin)
 .text          0x00000000        0x0 common.c.o (symbol from plugin)
 .text          0x00000000        0x0 eloop_none.c.o (symbol from plugin)
 .text          0x00000000        0x0 os_none.c.o (symbol from plugin)
 .text          0x00000000        0x0 wpa_debug.c.o (symbol from plugin)
 .text          0x00000000        0x0 wpabuf.c.o (symbol from plugin)
 .text          0x00000000        0x0 blacklist.c.o (symbol from plugin)
 .text          0x00000000        0x0 bss.c.o (symbol from plugin)
 .text          0x00000000        0x0 config.c.o (symbol from plugin)
 .text          0x00000000        0x0 config_none.c.o (symbol from plugin)
 .text          0x00000000        0x0 eap_register.c.o (symbol from plugin)
 .text          0x00000000        0x0 events.c.o (symbol from plugin)
 .text          0x00000000        0x0 notify.c.o (symbol from plugin)
 .text          0x00000000        0x0 op_classes.c.o (symbol from plugin)
 .text          0x00000000        0x0 rrm.c.o (symbol from plugin)
 .text          0x00000000        0x0 wmm_ac.c.o (symbol from plugin)
 .text          0x00000000        0x0 aes-unwrap.c.o (symbol from plugin)
 .text          0x00000000        0x0 md5-internal.c.o (symbol from plugin)
 .text          0x00000000        0x0 rc4.c.o (symbol from plugin)
 .text          0x00000000        0x0 driver_wext.c.o (symbol from plugin)
 .text          0x00000000        0x0 linux_ioctl.c.o (symbol from plugin)
 .text          0x00000000        0x0 netlink.c.o (symbol from plugin)
 .text          0x00000000        0x0 bitfield.c.o (symbol from plugin)
 .text          0x00000000        0x0 aes-internal-dec.c.o (symbol from plugin)
 .text          0x00000000        0x0 aes-internal.c.o (symbol from plugin)
 .text          0x00000000        0x0 http_cli.c.o (symbol from plugin)
 .text          0x00000000        0x0 https_cli.c.o (symbol from plugin)
 .text          0x00000000        0x0 json_tokener.c.o (symbol from plugin)
 .text          0x00000000        0x0 json_util.c.o (symbol from plugin)
 .text          0x00000000        0x0 json_object.c.o (symbol from plugin)
 .text          0x00000000        0x0 arraylist.c.o (symbol from plugin)
 .text          0x00000000        0x0 printbuf.c.o (symbol from plugin)
 .text          0x00000000        0x0 linkhash.c.o (symbol from plugin)
 .text          0x00000000        0x0 debug.c.o (symbol from plugin)
 .text          0x00000000        0x0 random_seed.c.o (symbol from plugin)
 .text          0x00000000        0x0 main.c.o (symbol from plugin)
 .text          0x00000000        0x0 iperf_api.c.o (symbol from plugin)
 .text          0x00000000        0x0 iperf_client_api.c.o (symbol from plugin)
 .text          0x00000000        0x0 iperf_error.c.o (symbol from plugin)
 .text          0x00000000        0x0 iperf_locale.c.o (symbol from plugin)
 .text          0x00000000        0x0 iperf_server_api.c.o (symbol from plugin)
 .text          0x00000000        0x0 iperf_tcp.c.o (symbol from plugin)
 .text          0x00000000        0x0 iperf_udp.c.o (symbol from plugin)
 .text          0x00000000        0x0 iperf_util.c.o (symbol from plugin)
 .text          0x00000000        0x0 net.c.o (symbol from plugin)
 .text          0x00000000        0x0 tcp_info.c.o (symbol from plugin)
 .text          0x00000000        0x0 timer.c.o (symbol from plugin)
 .text          0x00000000        0x0 units.c.o (symbol from plugin)
 .text          0x00000000        0x0 cjson.c.o (symbol from plugin)
 .text          0x00000000        0x0 dscp.c.o (symbol from plugin)
 .text          0x00000000        0x0 pk.c.o (symbol from plugin)
 .text          0x00000000        0x0 pkparse.c.o (symbol from plugin)
 .text          0x00000000        0x0 ctr_drbg.c.o (symbol from plugin)
 .text          0x00000000        0x0 certs.c.o (symbol from plugin)
 .text          0x00000000        0x0 pem.c.o (symbol from plugin)
 .text          0x00000000        0x0 x509_crt.c.o (symbol from plugin)
 .text          0x00000000        0x0 entropy.c.o (symbol from plugin)
 .text          0x00000000        0x0 ssl_tls.c.o (symbol from plugin)
 .text          0x00000000        0x0 base64.c.o (symbol from plugin)
 .text          0x00000000        0x0 oid.c.o (symbol from plugin)
 .text          0x00000000        0x0 md.c.o (symbol from plugin)
 .text          0x00000000        0x0 bignum.c.o (symbol from plugin)
 .text          0x00000000        0x0 asn1parse.c.o (symbol from plugin)
 .text          0x00000000        0x0 ssl_ciphersuites.c.o (symbol from plugin)
 .text          0x00000000        0x0 x509.c.o (symbol from plugin)
 .text          0x00000000        0x0 ssl_srv.c.o (symbol from plugin)
 .text          0x00000000        0x0 sha512.c.o (symbol from plugin)
 .text          0x00000000        0x0 sock_api_net.c.o (symbol from plugin)
 .text          0x00000000        0x0 entropy_poll.c.o (symbol from plugin)
 .text          0x00000000        0x0 rsa.c.o (symbol from plugin)
 .text          0x00000000        0x0 pk_wrap.c.o (symbol from plugin)
 .text          0x00000000        0x0 md5.c.o (symbol from plugin)
 .text          0x00000000        0x0 sha1.c.o (symbol from plugin)
 .text          0x00000000        0x0 dhm.c.o (symbol from plugin)
 .text          0x00000000        0x0 cipher.c.o (symbol from plugin)
 .text          0x00000000        0x0 ecp.c.o (symbol from plugin)
 .text          0x00000000        0x0 ecdh.c.o (symbol from plugin)
 .text          0x00000000        0x0 md_wrap.c.o (symbol from plugin)
 .text          0x00000000        0x0 ecp_curves.c.o (symbol from plugin)
 .text          0x00000000        0x0 ecdsa.c.o (symbol from plugin)
 .text          0x00000000        0x0 ssl_cli.c.o (symbol from plugin)
 .text          0x00000000        0x0 aes.c.o (symbol from plugin)
 .text          0x00000000        0x0 des.c.o (symbol from plugin)
 .text          0x00000000        0x0 sha256.c.o (symbol from plugin)
 .text          0x00000000        0x0 cipher_wrap.c.o (symbol from plugin)
 .text          0x00000000        0x0 timing.c.o (symbol from plugin)
 .text          0x00000000        0x0 algorithm_alt.c.o (symbol from plugin)
 .text          0x00000000        0x0 gcm.c.o (symbol from plugin)
 .text          0x00000000        0x0 asn1write.c.o (symbol from plugin)
 .text          0x00000000        0x0 dns.c.o (symbol from plugin)
 .text          0x00000000        0x0 stats.c.o (symbol from plugin)
 .text          0x00000000        0x0 memp.c.o (symbol from plugin)
 .text          0x00000000        0x0 def.c.o (symbol from plugin)
 .text          0x00000000        0x0 ip4_addr.c.o (symbol from plugin)
 .text          0x00000000        0x0 udp.c.o (symbol from plugin)
 .text          0x00000000        0x0 netif.c.o (symbol from plugin)
 .text          0x00000000        0x0 pbuf.c.o (symbol from plugin)
 .text          0x00000000        0x0 tcp.c.o (symbol from plugin)
 .text          0x00000000        0x0 ip.c.o (symbol from plugin)
 .text          0x00000000        0x0 netdb.c.o (symbol from plugin)
 .text          0x00000000        0x0 tcpip.c.o (symbol from plugin)
 .text          0x00000000        0x0 sockets.c.o (symbol from plugin)
 .text          0x00000000        0x0 err.c.o (symbol from plugin)
 .text          0x00000000        0x0 ethernet.c.o (symbol from plugin)
 .text          0x00000000        0x0 LwIP.c.o (symbol from plugin)
 .text          0x00000000        0x0 sys_arch.c.o (symbol from plugin)
 .text          0x00000000        0x0 wireless_ethernetif.c.o (symbol from plugin)
 .text          0x00000000        0x0 sock_api.c.o (symbol from plugin)
 .text          0x00000000        0x0 dhcp_srv.c.o (symbol from plugin)
 .text          0x00000000        0x0 ntp.c.o (symbol from plugin)
 .text          0x00000000        0x0 timeouts.c.o (symbol from plugin)
 .text          0x00000000        0x0 icmp.c.o (symbol from plugin)
 .text          0x00000000        0x0 ip4_frag.c.o (symbol from plugin)
 .text          0x00000000        0x0 ip4.c.o (symbol from plugin)
 .text          0x00000000        0x0 dhcp.c.o (symbol from plugin)
 .text          0x00000000        0x0 etharp.c.o (symbol from plugin)
 .text          0x00000000        0x0 igmp.c.o (symbol from plugin)
 .text          0x00000000        0x0 tcp_out.c.o (symbol from plugin)
 .text          0x00000000        0x0 init.c.o (symbol from plugin)
 .text          0x00000000        0x0 inet_chksum.c.o (symbol from plugin)
 .text          0x00000000        0x0 mem.c.o (symbol from plugin)
 .text          0x00000000        0x0 tcp_in.c.o (symbol from plugin)
 .text          0x00000000        0x0 wifi_connect.c.o (symbol from plugin)
 .text          0x00000000        0x0 ioctl.c.o (symbol from plugin)
 .text          0x00000000        0x0 net_device.c.o (symbol from plugin)
 .text          0x00000000        0x0 netlink_socket.c.o (symbol from plugin)
 .text          0x00000000        0x0 skbuff.c.o (symbol from plugin)
 .text          0x00000000        0x0 spinlock.c.o (symbol from plugin)
 .text          0x00000000        0x0 tasklet.c.o (symbol from plugin)
 .text          0x00000000        0x0 todo.c.o (symbol from plugin)
 .text          0x00000000        0x0 wext-core.c.o (symbol from plugin)
 .text          0x00000000        0x0 wext-priv.c.o (symbol from plugin)
 .text          0x00000000        0x0 wireless_main.c.o (symbol from plugin)
 .text          0x00000000        0x0 air_kiss.c.o (symbol from plugin)
 .text          0x00000000        0x0 simple_config.c.o (symbol from plugin)
 .text          0x00000000        0x0 rtmp_init.c.o (symbol from plugin)
 .text          0x00000000        0x0 rtmp_timer.c.o (symbol from plugin)
 .text          0x00000000        0x0 cmm_wpa.c.o (symbol from plugin)
 .text          0x00000000        0x0 mlme.c.o (symbol from plugin)
 .text          0x00000000        0x0 ba_action.c.o (symbol from plugin)
 .text          0x00000000        0x0 crypt_aes.c.o (symbol from plugin)
 .text          0x00000000        0x0 rtusb_io.c.o (symbol from plugin)
 .text          0x00000000        0x0 cmm_data_usb.c.o (symbol from plugin)
 .text          0x00000000        0x0 cmm_cfg.c.o (symbol from plugin)
 .text          0x00000000        0x0 crypt_hmac.c.o (symbol from plugin)
 .text          0x00000000        0x0 cmm_sanity.c.o (symbol from plugin)
 .text          0x00000000        0x0 cmm_aes.c.o (symbol from plugin)
 .text          0x00000000        0x0 cmm_mac_usb.c.o (symbol from plugin)
 .text          0x00000000        0x0 cmm_asic.c.o (symbol from plugin)
 .text          0x00000000        0x0 cmm_data.c.o (symbol from plugin)
 .text          0x00000000        0x0 action.c.o (symbol from plugin)
 .text          0x00000000        0x0 cmm_info.c.o (symbol from plugin)
 .text          0x00000000        0x0 cmm_tkip.c.o (symbol from plugin)
 .text          0x00000000        0x0 dfs.c.o (symbol from plugin)
 .text          0x00000000        0x0 rt_profile.c.o (symbol from plugin)
 .text          0x00000000        0x0 rt_main_dev.c.o (symbol from plugin)
 .text          0x00000000        0x0 usb_main_dev.c.o (symbol from plugin)
 .text          0x00000000        0x0 sta_ioctl.c.o (symbol from plugin)
 .text          0x00000000        0x0 connect.c.o (symbol from plugin)
 .text          0x00000000        0x0 auth_rsp.c.o (symbol from plugin)
 .text          0x00000000        0x0 sanity.c.o (symbol from plugin)
 .text          0x00000000        0x0 wpa.c.o (symbol from plugin)
 .text          0x00000000        0x0 assoc.c.o (symbol from plugin)
 .text          0x00000000        0x0 sync.c.o (symbol from plugin)
 .text          0x00000000        0x0 sta_cfg.c.o (symbol from plugin)
 .text          0x00000000        0x0 auth.c.o (symbol from plugin)
 .text          0x00000000        0x0 rtmp_data.c.o (symbol from plugin)
 .text          0x00000000        0x0 ap_auth.c.o (symbol from plugin)
 .text          0x00000000        0x0 ap_connect.c.o (symbol from plugin)
 .text          0x00000000        0x0 ap_sync.c.o (symbol from plugin)
 .text          0x00000000        0x0 ap_sanity.c.o (symbol from plugin)
 .text          0x00000000        0x0 ap_mlme.c.o (symbol from plugin)
 .text          0x00000000        0x0 ap_wpa.c.o (symbol from plugin)
 .text          0x00000000        0x0 ap_assoc.c.o (symbol from plugin)
 .text          0x00000000        0x0 ap.c.o (symbol from plugin)
 .text          0x00000000        0x0 ap_data.c.o (symbol from plugin)
 .text          0x00000000        0x0 ap_autoChSel.c.o (symbol from plugin)
 .text          0x00000000        0x0 p2p_cfg.c.o (symbol from plugin)
 .text          0x00000000        0x0 ap_ioctl.c.o (symbol from plugin)
 .text          0x00000000        0x0 wl_mp_test.c.o (symbol from plugin)
 .text          0x00000000        0x0 jl_todo.c.o (symbol from plugin)
 .text          0x00000000        0x0 os_priv.c.o (symbol from plugin)
 .text          0x00000000        0x0 rt_os_util.c.o (symbol from plugin)
 .text          0x00000000        0x0 rt_linux.c.o (symbol from plugin)
 .text          0x00000000        0x0 rt30xx.c.o (symbol from plugin)
 .text          0x00000000        0x0 rtmp_chip.c.o (symbol from plugin)
 .text          0x00000000        0x0 spectrum.c.o (symbol from plugin)
 .text          0x00000000        0x0 rtusb_bulk.c.o (symbol from plugin)
 .text          0x00000000        0x0 cmm_sync.c.o (symbol from plugin)
 .text          0x00000000        0x0 cmm_profile.c.o (symbol from plugin)
 .text          0x00000000        0x0 crypt_md5.c.o (symbol from plugin)
 .text          0x00000000        0x0 rtusb_data.c.o (symbol from plugin)
 .text          0x00000000        0x0 cmm_cmd.c.o (symbol from plugin)
 .text          0x00000000        0x0 cmm_wep.c.o (symbol from plugin)
 .text          0x00000000        0x0 rt_channel.c.o (symbol from plugin)
 .text          0x00000000        0x0 rtmp_init_inf.c.o (symbol from plugin)
 .text          0x00000000        0x0 crypt_arc4.c.o (symbol from plugin)
 .text          0x00000000        0x0 crypt_sha2.c.o (symbol from plugin)
 .text          0x00000000        0x0 rt_usb.c.o (symbol from plugin)
 .text          0x00000000        0x0 wait.c.o (symbol from plugin)
 .text          0x00000000        0x0 rt_usb_util.c.o (symbol from plugin)
 .text          0x00000000        0x0 sd.c.o (symbol from plugin)
 .text          0x00000000        0x0 wlc.c.o (symbol from plugin)
 .text          0x00000000        0x0 aes_hw.c.o (symbol from plugin)
 .text          0x00000000        0x0 sha_hw.c.o (symbol from plugin)
 .text          0x00000000        0x0 rand.c.o (symbol from plugin)
 .text          0x00000000        0x0 vfprintf.c.o (symbol from plugin)
 .text          0x00000000        0x0 strftime.c.o (symbol from plugin)
 .text          0x00000000        0x0 getopt.c.o (symbol from plugin)
 .text          0x00000000        0x0 assert.c.o (symbol from plugin)
 .text          0x00000000        0x0 cmpt_todo.c.o (symbol from plugin)
 .text          0x00000000        0x0 hsm.c.o (symbol from plugin)

Memory Configuration

Name             Origin             Length             Attributes
rom              0x02000120         0x01000000         xr
sdram            0x04000120         0x00800000         xrw
ram0             0x01c00000         0x0007fd4c         xrw
boot_info        0x01c7fd4c         0x00000034         xrw
cache_ram        0x01f28000         0x00007000         rw
*default*        0x00000000         0xffffffff

Linker script and memory map

LOAD obj/Release/cpu/wl82/debug.o
LOAD ..\..\..\..\..\cpu\wl82\tools\sdk.elf.o
LOAD obj/Release/cpu/wl82/iic.o
LOAD obj/Release/cpu/wl82/key/adc_api.o
LOAD obj/Release/cpu/wl82/key/ctmu.o
LOAD obj/Release/cpu/wl82/key/irflt.o
LOAD obj/Release/cpu/wl82/key/plcnt.o
LOAD obj/Release/cpu/wl82/key/rdec.o
LOAD obj/Release/cpu/wl82/port_waked_up.o
LOAD obj/Release/cpu/wl82/setup.o
LOAD obj/Release/include_lib/btctrler/version.z.o
LOAD obj/Release/include_lib/btstack/version.z.o
LOAD obj/Release/include_lib/driver/version.z.o
LOAD obj/Release/include_lib/media/version.z.o
LOAD obj/Release/include_lib/net/version.z.o
LOAD obj/Release/include_lib/server/version.z.o
LOAD obj/Release/include_lib/system/version.z.o
LOAD obj/Release/include_lib/update/version.z.o
LOAD obj/Release/include_lib/utils/version.z.o
LOAD obj/Release/lib/net/version.z.o
LOAD obj/Release/lib/server/version.z.o
LOAD obj/Release/lib/utils/version.z.o
LOAD obj/Release/apps/common/config/log_config/lib_driver_config.o
LOAD obj/Release/apps/common/config/log_config/lib_system_config.o
LOAD obj/Release/apps/common/config/log_config/lib_update_config.o
LOAD obj/Release/apps/common/config/user_cfg.o
LOAD obj/Release/apps/common/debug/debug.o
LOAD obj/Release/apps/common/debug/debug_user.o
LOAD obj/Release/apps/common/iic/iic.o
LOAD obj/Release/apps/common/iic/software_iic.o
LOAD obj/Release/apps/common/net/assign_macaddr.o
LOAD obj/Release/apps/common/net/config_network.o
LOAD obj/Release/apps/common/net/iperf_test.o
LOAD obj/Release/apps/common/net/platform_cfg.o
LOAD obj/Release/apps/common/net/wifi_conf.o
LOAD obj/Release/apps/common/rf_fcc_tool/bt.o
LOAD obj/Release/apps/common/rf_fcc_tool/rf_fcc_main.o
LOAD obj/Release/apps/common/system/init.o
LOAD obj/Release/apps/common/system/system_reset_reason.o
LOAD obj/Release/apps/common/system/version.o
LOAD obj/Release/apps/common/update/update.o
LOAD obj/Release/apps/demo/demo_wifi/app_main.o
LOAD obj/Release/apps/demo/demo_wifi/epd_touch/epd/epd_display.o
LOAD obj/Release/apps/demo/demo_wifi/epd_touch/epd/epd_display_new.o
LOAD obj/Release/apps/demo/demo_wifi/epd_touch/epd/epd_partial_s37t03.o
LOAD obj/Release/apps/demo/demo_wifi/epd_touch/epd/epd_predefined_data.o
LOAD obj/Release/apps/demo/demo_wifi/epd_touch/epd/epd_spi.o
LOAD obj/Release/apps/demo/demo_wifi/epd_touch/epd/epd_unified.o
LOAD obj/Release/apps/demo/demo_wifi/epd_touch/epd_touch_api.o
LOAD obj/Release/apps/demo/demo_wifi/epd_touch/examples/epd_touch_advanced_example.o
LOAD obj/Release/apps/demo/demo_wifi/epd_touch/examples/epd_touch_example.o
LOAD obj/Release/apps/demo/demo_wifi/epd_touch/fonts/font12.o
LOAD obj/Release/apps/demo/demo_wifi/epd_touch/fonts/font12CN.o
LOAD obj/Release/apps/demo/demo_wifi/epd_touch/fonts/font16.o
LOAD obj/Release/apps/demo/demo_wifi/epd_touch/fonts/font20.o
LOAD obj/Release/apps/demo/demo_wifi/epd_touch/fonts/font24.o
LOAD obj/Release/apps/demo/demo_wifi/epd_touch/fonts/font24CN.o
LOAD obj/Release/apps/demo/demo_wifi/epd_touch/fonts/font8.o
LOAD obj/Release/apps/demo/demo_wifi/epd_touch/gui/gui_paint.o
LOAD obj/Release/apps/demo/demo_wifi/epd_touch/hardware/exti.o
LOAD obj/Release/apps/demo/demo_wifi/epd_touch/hardware/hardware_config.o
LOAD obj/Release/apps/demo/demo_wifi/epd_touch/hardware/key.o
LOAD obj/Release/apps/demo/demo_wifi/epd_touch/hardware/led.o
LOAD obj/Release/apps/demo/demo_wifi/epd_touch/hardware/power_mgmt.o
LOAD obj/Release/apps/demo/demo_wifi/epd_touch/hardware/system_check.o
LOAD obj/Release/apps/demo/demo_wifi/epd_touch/test/epd_touch_test.o
LOAD obj/Release/apps/demo/demo_wifi/epd_touch/touch/cst836u.o
LOAD obj/Release/apps/demo/demo_wifi/llm_chat.o
LOAD obj/Release/apps/demo/demo_wifi/llm_chat_ui.o
LOAD obj/Release/apps/demo/demo_wifi/llm_http.o
LOAD obj/Release/apps/demo/demo_wifi/llm_json.o
LOAD obj/Release/apps/demo/demo_wifi/wifi_demo_task.o
LOAD obj/Release/apps/demo/demo_wifi/board/wl82/board.o
START GROUP
LOAD ../../../../../include_lib/newlib/pi32v2-lib/libm.a
LOAD ../../../../../include_lib/newlib/pi32v2-lib/libc.a
LOAD ../../../../../include_lib/newlib/pi32v2-lib/libcompiler_rt.a
START GROUP
LOAD ../../../../../cpu/wl82/liba/cpu.a
LOAD ../../../../../cpu/wl82/liba/fs.a
LOAD ../../../../../cpu/wl82/liba/event.a
LOAD ../../../../../cpu/wl82/liba/system.a
LOAD ../../../../../cpu/wl82/liba/cfg_tool.a
LOAD ../../../../../cpu/wl82/liba/common_lib.a
LOAD ../../../../../cpu/wl82/liba/update.a
LOAD ../../../../../cpu/wl82/liba/wl_rf_common.a
LOAD ../../../../../cpu/wl82/liba/hsm.a
LOAD ../../../../../cpu/wl82/liba/wpasupplicant.a
LOAD ../../../../../cpu/wl82/liba/http_cli.a
LOAD ../../../../../cpu/wl82/liba/json.a
LOAD ../../../../../cpu/wl82/liba/iperf.a
LOAD ../../../../../cpu/wl82/liba/libmbedtls_2_2_1.a
LOAD ../../../../../cpu/wl82/liba/lwip_2_1_3_sfc.a
LOAD ../../../../../cpu/wl82/liba/wl_wifi_sfc.a
END GROUP
                0x00000034                BOOT_INFO_SIZE = 0x34
                0x00000000                TLB_SIZE = 0x0
                0x00800000                SDRAM_SIZE = 0x800000
                0x0007fd4c                RAM0_SIZE = (((0x7fe00 - TLB_SIZE) - BOOT_INFO_SIZE) - 0x80)
                0x01c7fd80                UPDATA_BEG = 0x1c7fd80
                0x00000007                FREE_DACHE_WAY = 0x7
                0x00000000                FREE_IACHE_WAY = 0x0
                0x00007000                CACHE_RAM_SIZE = ((FREE_DACHE_WAY * 0x1000) + (FREE_IACHE_WAY * 0x1000))
                0x02000120                . = ORIGIN (rom)

.text           0x02000120    0xc2b00
                0x02000120                _text_rodata_begin = .
                [!provide]                PROVIDE (text_rodata_begin, .)
 *startup.S.o(.text)
 .text          0x02000120       0xc4 ../../../../../cpu/wl82/liba/cpu.a(startup.S.o)
                0x02000120                _start
                0x02000122                wait_if_breakpoint
                0x020001b8                cpu1_start
                0x020001cc                exception_irq_handler
 *(.boot_code)
 .boot_code     0x020001e4      0x294 ..\..\..\..\..\cpu\wl82\tools\sdk.elf.o
                0x0200029c                boot_sdram_con_config
                0x020003c2                boot_info_init
                0x0200040e                boot_memmove
 *(.text*)
 .text          0x02000478    0x8dd4e ..\..\..\..\..\cpu\wl82\tools\sdk.elf.o
                0x02001bfe                exception_analyze
                0x02001f7e                sp_ovf_unen
                0x02001f92                sp_ovf_en
                0x0200ca24                __errno
                0x0200ca2c                __rt_local_irq_disable
                0x0200ca5c                __rt_local_irq_enable
                0x0200cd40                cpu1_main
                0x0200d2a8                main
                0x0200f2ee                update_result_get
 .text          0x0208e1c6        0x0 obj/Release/include_lib/btctrler/version.z.o
 .text          0x0208e1c6        0x0 obj/Release/include_lib/btstack/version.z.o
 .text          0x0208e1c6        0x0 obj/Release/include_lib/driver/version.z.o
 .text          0x0208e1c6        0x0 obj/Release/include_lib/media/version.z.o
 .text          0x0208e1c6        0x0 obj/Release/include_lib/net/version.z.o
 .text          0x0208e1c6        0x0 obj/Release/include_lib/server/version.z.o
 .text          0x0208e1c6        0x0 obj/Release/include_lib/system/version.z.o
 .text          0x0208e1c6        0x0 obj/Release/include_lib/update/version.z.o
 .text          0x0208e1c6        0x0 obj/Release/include_lib/utils/version.z.o
 .text          0x0208e1c6        0x0 obj/Release/lib/net/version.z.o
 .text          0x0208e1c6        0x0 obj/Release/lib/server/version.z.o
 .text          0x0208e1c6        0x0 obj/Release/lib/utils/version.z.o
 .text          0x0208e1c6       0x38 ../../../../../include_lib/newlib/pi32v2-lib/libc.a(lib_a-memcmp.o)
                0x0208e1c6                memcmp
 .text          0x0208e1fe       0xa0 ../../../../../include_lib/newlib/pi32v2-lib/libc.a(lib_a-strcmp.o)
                0x0208e1fe                strcmp
 .text          0x0208e29e       0x6e ../../../../../include_lib/newlib/pi32v2-lib/libc.a(lib_a-strcpy.o)
                0x0208e29e                strcpy
 .text          0x0208e30c        0xc ../../../../../include_lib/newlib/pi32v2-lib/libc.a(lib_a-strlen.o)
                0x0208e30c                strlen
 .text          0x0208e318       0x1a ../../../../../include_lib/newlib/pi32v2-lib/libc.a(lib_a-strncmp.o)
                0x0208e318                strncmp
 .text          0x0208e332       0x26 ../../../../../include_lib/newlib/pi32v2-lib/libc.a(lib_a-strncpy.o)
                0x0208e332                strncpy
 .text          0x0208e358       0x2a ../../../../../include_lib/newlib/pi32v2-lib/libc.a(lib_a-strstr.o)
                0x0208e358                strstr
 .text          0x0208e382        0x0 ../../../../../cpu/wl82/liba/cpu.a(version.z.o)
 .text          0x0208e382        0x0 ../../../../../cpu/wl82/liba/fs.a(version.z.o)
 .text          0x0208e382        0x0 ../../../../../cpu/wl82/liba/event.a(version.z.o)
 .text          0x0208e382       0x78 ../../../../../cpu/wl82/liba/system.a(port_asm.S.o)
                0x0208e382                vSoftwareInterruptISR
 .text          0x0208e3fa        0x0 ../../../../../cpu/wl82/liba/system.a(movable.S.o)
 .text          0x0208e3fa        0x0 ../../../../../cpu/wl82/liba/system.a(version.z.o)
 .text          0x0208e3fa        0x0 ../../../../../cpu/wl82/liba/cfg_tool.a(version.z.o)
 .text          0x0208e3fa        0x0 ../../../../../cpu/wl82/liba/common_lib.a(version.z.o)
 .text          0x0208e3fa        0x0 ../../../../../cpu/wl82/liba/update.a(version.z.o)
 .text          0x0208e3fa        0x0 ../../../../../cpu/wl82/liba/wl_rf_common.a(version.z.o)
 .text          0x0208e3fa        0x0 ../../../../../cpu/wl82/liba/hsm.a(version.z.o)
 .text          0x0208e3fa        0x0 ../../../../../cpu/wl82/liba/wpasupplicant.a(version.z.o)
 .text          0x0208e3fa        0x0 ../../../../../cpu/wl82/liba/http_cli.a(version.z.o)
 .text          0x0208e3fa        0x0 ../../../../../cpu/wl82/liba/json.a(version.z.o)
 .text          0x0208e3fa        0x0 ../../../../../cpu/wl82/liba/iperf.a(version.z.o)
 .text          0x0208e3fa        0x0 ../../../../../cpu/wl82/liba/libmbedtls_2_2_1.a(version.z.o)
 .text          0x0208e3fa        0x0 ../../../../../cpu/wl82/liba/lwip_2_1_3_sfc.a(version.z.o)
 .text          0x0208e3fa        0x0 ../../../../../cpu/wl82/liba/wl_wifi_sfc.a(version.z.o)
 .text          0x0208e3fa       0x62 ../../../../../include_lib/newlib/pi32v2-lib/libm.a(lib_a-s_fpclassify.o)
                0x0208e3fa                __fpclassifyd
 .text          0x0208e45c       0x8e ../../../../../include_lib/newlib/pi32v2-lib/libm.a(lib_a-s_modf.o)
                0x0208e45c                modf
 .text          0x0208e4ea       0x10 ../../../../../include_lib/newlib/pi32v2-lib/libc.a(lib_a-atoi.o)
                0x0208e4ea                atoi
                0x0208e4f2                _atoi_r
 .text          0x0208e4fa       0x40 ../../../../../include_lib/newlib/pi32v2-lib/libc.a(lib_a-bsearch.o)
                0x0208e4fa                bsearch
 .text          0x0208e53a        0x0 ../../../../../include_lib/newlib/pi32v2-lib/libc.a(lib_a-ctype_.o)
 .text          0x0208e53a      0x150 ../../../../../include_lib/newlib/pi32v2-lib/libc.a(lib_a-gmtime_r.o)
                0x0208e53a                gmtime_r
 .text          0x0208e68a        0x0 ../../../../../include_lib/newlib/pi32v2-lib/libc.a(lib_a-impure.o)
 .text          0x0208e68a       0x1a ../../../../../include_lib/newlib/pi32v2-lib/libc.a(lib_a-islower.o)
                0x0208e68a                islower
 .text          0x0208e6a4       0x12 ../../../../../include_lib/newlib/pi32v2-lib/libc.a(lib_a-isspace.o)
                0x0208e6a4                isspace
 .text          0x0208e6b6       0x6c ../../../../../include_lib/newlib/pi32v2-lib/libc.a(lib_a-locale.o)
                0x0208e6b6                _setlocale_r
                0x0208e6f0                __locale_charset
                0x0208e6f8                __locale_mb_cur_max
                0x0208e702                __locale_msgcharset
                0x0208e70a                __locale_cjk_lang
                0x0208e70e                _localeconv_r
                0x0208e716                setlocale
                0x0208e71a                localeconv
 .text          0x0208e722       0x2c ../../../../../include_lib/newlib/pi32v2-lib/libc.a(lib_a-memset.o)
                0x0208e722                memset
 .text          0x0208e74e      0x2ae ../../../../../include_lib/newlib/pi32v2-lib/libc.a(lib_a-qsort.o)
                0x0208e7b8                qsort
 .text          0x0208e9fc       0x4e ../../../../../include_lib/newlib/pi32v2-lib/libc.a(lib_a-rand.o)
                0x0208e9fc                srand
                0x0208ea0c                rand
 .text          0x0208ea4a       0x3e ../../../../../include_lib/newlib/pi32v2-lib/libc.a(lib_a-strcasecmp.o)
                0x0208ea4a                strcasecmp
 .text          0x0208ea88       0x18 ../../../../../include_lib/newlib/pi32v2-lib/libc.a(lib_a-strcat.o)
                0x0208ea88                strcat
 .text          0x0208eaa0       0x14 ../../../../../include_lib/newlib/pi32v2-lib/libc.a(lib_a-strchr.o)
                0x0208eaa0                strchr
 .text          0x0208eab4       0x46 ../../../../../include_lib/newlib/pi32v2-lib/libc.a(lib_a-strncasecmp.o)
                0x0208eab4                strncasecmp
 .text          0x0208eafa       0x26 ../../../../../include_lib/newlib/pi32v2-lib/libc.a(lib_a-strncat.o)
                0x0208eafa                strncat
 .text          0x0208eb20       0x36 ../../../../../include_lib/newlib/pi32v2-lib/libc.a(lib_a-strpbrk.o)
                0x0208eb20                strpbrk
 .text          0x0208eb56       0x24 ../../../../../include_lib/newlib/pi32v2-lib/libc.a(lib_a-strrchr.o)
                0x0208eb56                strrchr
 .text          0x0208eb7a        0xa ../../../../../include_lib/newlib/pi32v2-lib/libc.a(lib_a-strsep.o)
                0x0208eb7a                strsep
 .text          0x0208eb84       0x24 ../../../../../include_lib/newlib/pi32v2-lib/libc.a(lib_a-strspn.o)
                0x0208eb84                strspn
 .text          0x0208eba8       0x54 ../../../../../include_lib/newlib/pi32v2-lib/libc.a(lib_a-strtok_r.o)
                0x0208eba8                __strtok_r
                0x0208ebf8                strtok_r
 .text          0x0208ebfc       0x1a ../../../../../include_lib/newlib/pi32v2-lib/libc.a(lib_a-strtoll.o)
                0x0208ebfc                strtoll
 .text          0x0208ec16      0x15c ../../../../../include_lib/newlib/pi32v2-lib/libc.a(lib_a-strtoll_r.o)
                0x0208ec16                _strtoll_r
 .text          0x0208ed72      0x108 ../../../../../include_lib/newlib/pi32v2-lib/libc.a(lib_a-strtol.o)
                0x0208ed72                _strtol_r
                0x0208ee62                strtol
 .text          0x0208ee7a       0xfe ../../../../../include_lib/newlib/pi32v2-lib/libc.a(lib_a-strtoul.o)
                0x0208ee7a                _strtoul_r
                0x0208ef60                strtoul
 .text          0x0208ef78       0x1e ../../../../../include_lib/newlib/pi32v2-lib/libc.a(lib_a-tolower.o)
                0x0208ef78                tolower
 .text          0x0208ef96       0x1e ../../../../../include_lib/newlib/pi32v2-lib/libc.a(lib_a-toupper.o)
                0x0208ef96                toupper
 .text          0x0208efb4        0xc ../../../../../include_lib/newlib/pi32v2-lib/libcompiler_rt.a(subdf3.o)
                0x0208efb4                __subdf3
 .text          0x0208efc0       0x12 ../../../../../include_lib/newlib/pi32v2-lib/libcompiler_rt.a(udivdi3.o)
                0x0208efc0                __udivdi3
 .text          0x0208efd2      0x226 ../../../../../include_lib/newlib/pi32v2-lib/libcompiler_rt.a(udivmoddi4.o)
                0x0208efd2                __udivmoddi4
 .text          0x0208f1f8       0x16 ../../../../../include_lib/newlib/pi32v2-lib/libcompiler_rt.a(umoddi3.o)
                0x0208f1f8                __umoddi3
 .text          0x0208f20e      0x30e ../../../../../include_lib/newlib/pi32v2-lib/libcompiler_rt.a(adddf3.o)
                0x0208f23c                __adddf3
 .text          0x0208f51c       0x18 ../../../../../include_lib/newlib/pi32v2-lib/libc.a(lib_a-memchr.o)
                0x0208f51c                memchr
 .text          0x0208f534       0xc2 ../../../../../include_lib/newlib/pi32v2-lib/libc.a(lib_a-memcpy.o)
                0x0208f534                memcpy
                0x0208f534                memmove
 .text          0x0208f5f6      0x1b8 ../../../../../include_lib/newlib/pi32v2-lib/libcompiler_rt.a(comparedf2.o)
                0x0208f5f6                __ledf2
                0x0208f5f6                __cmpdf2
                0x0208f6a6                __gedf2
                0x0208f74e                __unorddf2
                0x0208f78e                __eqdf2
                0x0208f796                __ltdf2
                0x0208f79e                __nedf2
                0x0208f7a6                __gtdf2
 .text          0x0208f7ae      0x338 ../../../../../include_lib/newlib/pi32v2-lib/libcompiler_rt.a(divdf3.o)
                0x0208f7d6                __divdf3
 .text          0x0208fae6       0x7a ../../../../../include_lib/newlib/pi32v2-lib/libcompiler_rt.a(extendsfdf2.o)
                0x0208fae6                __extendsfdf2
 .text          0x0208fb60       0x32 ../../../../../include_lib/newlib/pi32v2-lib/libcompiler_rt.a(fixdfdi.o)
                0x0208fb60                __fixdfdi
 .text          0x0208fb92       0x54 ../../../../../include_lib/newlib/pi32v2-lib/libcompiler_rt.a(fixdfsi.o)
                0x0208fb92                __fixdfsi
 .text          0x0208fbe6       0x58 ../../../../../include_lib/newlib/pi32v2-lib/libcompiler_rt.a(fixunsdfdi.o)
                0x0208fbe6                __fixunsdfdi
 .text          0x0208fc3e       0x3c ../../../../../include_lib/newlib/pi32v2-lib/libcompiler_rt.a(fixunsdfsi.o)
                0x0208fc3e                __fixunsdfsi
 .text          0x0208fc7a       0x3a ../../../../../include_lib/newlib/pi32v2-lib/libcompiler_rt.a(floatdidf.o)
                0x0208fc7a                __floatdidf
 .text          0x0208fcb4       0x4c ../../../../../include_lib/newlib/pi32v2-lib/libcompiler_rt.a(floatsidf.o)
                0x0208fcb4                __floatsidf
 .text          0x0208fd00       0x2c ../../../../../include_lib/newlib/pi32v2-lib/libcompiler_rt.a(floatundidf.o)
                0x0208fd00                __floatundidf
 .text          0x0208fd2c       0x34 ../../../../../include_lib/newlib/pi32v2-lib/libcompiler_rt.a(floatunsidf.o)
                0x0208fd2c                __floatunsidf
 .text          0x0208fd60      0x2ec ../../../../../include_lib/newlib/pi32v2-lib/libcompiler_rt.a(muldf3.o)
                0x0208fd88                __muldf3
 .text          0x0209004c      0x146 ../../../../../include_lib/newlib/pi32v2-lib/libcompiler_rt.a(truncdfsf2.o)
                0x0209004c                __truncdfsf2
 *(.rodata*)
 *fill*         0x02090192        0xe 
 .rodata.str1.16
                0x020901a0     0x6ec0 ..\..\..\..\..\cpu\wl82\tools\sdk.elf.o
                               0x6eba (size before relaxing)
 .rodata.str1.1
                0x02097060     0x2c6c ..\..\..\..\..\cpu\wl82\tools\sdk.elf.o
                               0x2cfc (size before relaxing)
 *fill*         0x02099ccc       0x14 
 .rodata        0x02099ce0    0x1f4c0 ..\..\..\..\..\cpu\wl82\tools\sdk.elf.o
 .rodata.cst32  0x020b91a0       0x60 ..\..\..\..\..\cpu\wl82\tools\sdk.elf.o
 .rodata.cst16  0x020b9200       0x20 ..\..\..\..\..\cpu\wl82\tools\sdk.elf.o
 .rodata.cst8   0x020b9220       0x10 ..\..\..\..\..\cpu\wl82\tools\sdk.elf.o
 .rodata        0x020b9230      0x281 ../../../../../include_lib/newlib/pi32v2-lib/libc.a(lib_a-ctype_.o)
                0x020b93b0                _ctype_
 *fill*         0x020b94b1        0x3 
 .rodata        0x020b94b4        0x4 ../../../../../include_lib/newlib/pi32v2-lib/libc.a(lib_a-impure.o)
                0x020b94b4                _global_impure_ptr
 .rodata.str1.1
                0x020b94b8        0x0 ../../../../../include_lib/newlib/pi32v2-lib/libc.a(lib_a-impure.o)
                                  0x2 (size before relaxing)
 .rodata.str1.1
                0x020b94b8        0x6 ../../../../../include_lib/newlib/pi32v2-lib/libc.a(lib_a-locale.o)
                                  0xb (size before relaxing)
 *(.LOG_TAG_CONST*)
 *(.fat_data_code)
 .fat_data_code
                0x020b94be      0x9b4 ..\..\..\..\..\cpu\wl82\tools\sdk.elf.o
                0x020b9e74                . = ALIGN (0x4)
 *fill*         0x020b9e72        0x2 
 *(.update_const)
 *(.update_code)
 .update_code   0x020b9e74       0x46 ..\..\..\..\..\cpu\wl82\tools\sdk.elf.o
 *(.bt_updata_ram_code)
                0x020b9ebc                . = ALIGN (0x4)
 *fill*         0x020b9eba        0x2 
                0x020b9ebc                __VERSION_BEGIN = .
 *(.sys.version)
 .sys.version   0x020b9ebc       0x1a obj/Release/include_lib/btctrler/version.z.o
 .sys.version   0x020b9ed6       0x19 obj/Release/include_lib/btstack/version.z.o
                0x020b9ed6                include_lib_btstack_version
 .sys.version   0x020b9eef       0x18 obj/Release/include_lib/driver/version.z.o
                0x020b9eef                include_lib_driver_version
 .sys.version   0x020b9f07       0x17 obj/Release/include_lib/media/version.z.o
                0x020b9f07                include_lib_media_version
 .sys.version   0x020b9f1e       0x15 obj/Release/include_lib/net/version.z.o
                0x020b9f1e                include_lib_net_version
 .sys.version   0x020b9f33       0x18 obj/Release/include_lib/server/version.z.o
                0x020b9f33                include_lib_server_version
 .sys.version   0x020b9f4b       0x18 obj/Release/include_lib/system/version.z.o
                0x020b9f4b                include_lib_system_version
 .sys.version   0x020b9f63       0x18 obj/Release/include_lib/update/version.z.o
                0x020b9f63                include_lib_update_version
 .sys.version   0x020b9f7b       0x17 obj/Release/include_lib/utils/version.z.o
                0x020b9f7b                include_lib_utils_version
 .sys.version   0x020b9f92        0xe obj/Release/lib/net/version.z.o
                0x020b9f92                lib_net_version
 .sys.version   0x020b9fa0       0x10 obj/Release/lib/server/version.z.o
                0x020b9fa0                lib_server_version
 .sys.version   0x020b9fb0        0xf obj/Release/lib/utils/version.z.o
                0x020b9fb0                lib_utils_version
 .sys.version   0x020b9fbf       0x11 ../../../../../cpu/wl82/liba/cpu.a(version.z.o)
                0x020b9fbf                lib_driver_version
 .sys.version   0x020b9fd0        0x0 ../../../../../cpu/wl82/liba/fs.a(version.z.o)
                0x020b9fd0                lib_fs_version
 .sys.version   0x020b9fd0        0x0 ../../../../../cpu/wl82/liba/event.a(version.z.o)
                0x020b9fd0                lib_event_version
 .sys.version   0x020b9fd0       0x10 ../../../../../cpu/wl82/liba/system.a(version.z.o)
                0x020b9fd0                lib_system_version
 .sys.version   0x020b9fe0        0x0 ../../../../../cpu/wl82/liba/cfg_tool.a(version.z.o)
                0x020b9fe0                lib_cfg_tool_version
 .sys.version   0x020b9fe0        0x0 ../../../../../cpu/wl82/liba/common_lib.a(version.z.o)
                0x020b9fe0                lib_common_version
 .sys.version   0x020b9fe0       0x10 ../../../../../cpu/wl82/liba/update.a(version.z.o)
                0x020b9fe0                lib_update_version
 .sys.version   0x020b9ff0        0x0 ../../../../../cpu/wl82/liba/wl_rf_common.a(version.z.o)
                0x020b9ff0                lib_wl82_rf_version
 .sys.version   0x020b9ff0        0x0 ../../../../../cpu/wl82/liba/hsm.a(version.z.o)
                0x020b9ff0                lib_hsm_version
 .sys.version   0x020b9ff0        0x0 ../../../../../cpu/wl82/liba/wpasupplicant.a(version.z.o)
                0x020b9ff0                lib_wpa_supplicant_version
 .sys.version   0x020b9ff0        0x0 ../../../../../cpu/wl82/liba/http_cli.a(version.z.o)
                0x020b9ff0                lib_httpcli_version
 .sys.version   0x020b9ff0        0x0 ../../../../../cpu/wl82/liba/json.a(version.z.o)
                0x020b9ff0                lib_json_c_version
 .sys.version   0x020b9ff0        0x0 ../../../../../cpu/wl82/liba/iperf.a(version.z.o)
                0x020b9ff0                lib_iperf_version
 .sys.version   0x020b9ff0        0x0 ../../../../../cpu/wl82/liba/libmbedtls_2_2_1.a(version.z.o)
                0x020b9ff0                lib_mbedtls_2_2_1_version
 .sys.version   0x020b9ff0        0x0 ../../../../../cpu/wl82/liba/lwip_2_1_3_sfc.a(version.z.o)
                0x020b9ff0                lib_lwip_2_1_3_version
 .sys.version   0x020b9ff0        0x0 ../../../../../cpu/wl82/liba/wl_wifi_sfc.a(version.z.o)
                0x020b9ff0                lib_wifi_version
                0x020b9ff0                __VERSION_END = .
 *(.dlmalloc_code)
 .dlmalloc_code
                0x020b9ff0     0x333a ..\..\..\..\..\cpu\wl82\tools\sdk.elf.o
                0x020bd30c                memory_init
 *(.dlmalloc_const)
 *(.mem_heap_code)
 *(.mem_heap_const)
                0x020bd32a                _os_begin = .
                [!provide]                PROVIDE (os_begin, .)
 *(.os_code)
 .os_code       0x020bd32a     0x3cb4 ..\..\..\..\..\cpu\wl82\tools\sdk.elf.o
                0x020c01fe                OS_ClrPending
 *(.os_const)
 *(.os_str)
 .os_str        0x020c0fde      0x90f ..\..\..\..\..\cpu\wl82\tools\sdk.elf.o
 *(.os_critical_code)
 *fill*         0x020c18ed        0x1 
 .os_critical_code
                0x020c18ee       0xdc ..\..\..\..\..\cpu\wl82\tools\sdk.elf.o
                0x020c18ee                jiffies_half_msec
                0x020c1946                jiffies_msec
                0x020c194e                os_current_task
 *(.os_port_code)
 *(.os_port_const)
                0x020c19ca                _os_end = .
                [!provide]                PROVIDE (os_end, .)
 *(.movable.stub.1)
                0x020c19ca                __rf_trim_movable_region_start = .
 *(.movable.region.1)
 .movable.region.1
                0x020c19ca      0xec0 ..\..\..\..\..\cpu\wl82\tools\sdk.elf.o
                0x020c288a                __rf_trim_movable_region_end = .
 *(.jlsp_code)
                0x020c288c                . = ALIGN (0x4)
 *fill*         0x020c288a        0x2 
 *(.jlsp_const)
 *(.crossOver_sparse_code)
                0x020c288c                . = ALIGN (0x4)
                0x020c288c                _vfs_ops_begin = .
                0x020c288c                PROVIDE (vfs_ops_begin, .)
 *(.vfs_operations)
 .vfs_operations
                0x020c288c      0x258 ..\..\..\..\..\cpu\wl82\tools\sdk.elf.o
                0x020c288c                nor_sdfile_vfs_ops
                0x020c2904                sdfile_vfs_ops
                0x020c297c                sdfile_ext_vfs_ops
                0x020c29f4                fat_sdfile_fat_ops
                0x020c2a6c                jl_fat_vfs_ops
                0x020c2ae4                _vfs_ops_end = .
                0x020c2ae4                PROVIDE (vfs_ops_end, .)
                0x020c2ae4                _initcall_begin = .
                0x020c2ae4                PROVIDE (initcall_begin, .)
 *(.initcall)
 .initcall      0x020c2ae4        0x4 ..\..\..\..\..\cpu\wl82\tools\sdk.elf.o
                0x020c2ae4                __initcall_app_update_init
                0x020c2ae8                _initcall_end = .
                0x020c2ae8                PROVIDE (initcall_end, .)
                0x020c2ae8                _early_initcall_begin = .
                0x020c2ae8                PROVIDE (early_initcall_begin, .)
 *(.early.initcall)
 .early.initcall
                0x020c2ae8       0x14 ..\..\..\..\..\cpu\wl82\tools\sdk.elf.o
                0x020c2ae8                __initcall_app_version_check
                0x020c2aec                __initcall_sdfile_init
                0x020c2af0                __initcall_sys_event_init
                0x020c2af4                __initcall_thread_fork_init
                0x020c2af8                __initcall_ntp_client_init
                0x020c2afc                _early_initcall_end = .
                0x020c2afc                PROVIDE (early_initcall_end, .)
                0x020c2afc                _late_initcall_begin = .
                0x020c2afc                PROVIDE (late_initcall_begin, .)
 *(.late.initcall)
 .late.initcall
                0x020c2afc        0x8 ..\..\..\..\..\cpu\wl82\tools\sdk.elf.o
                0x020c2afc                __initcall_demo_wifi
                0x020c2b00                __initcall_sdk_meky_check
                0x020c2b04                _late_initcall_end = .
                0x020c2b04                PROVIDE (late_initcall_end, .)
                0x020c2b04                _platform_initcall_begin = .
                0x020c2b04                PROVIDE (platform_initcall_begin, .)
 *(.platform.initcall)
 .platform.initcall
                0x020c2b04        0x4 ..\..\..\..\..\cpu\wl82\tools\sdk.elf.o
                0x020c2b04                __initcall_syscfg_tools_init
                0x020c2b08                _platform_initcall_end = .
                0x020c2b08                PROVIDE (platform_initcall_end, .)
                0x020c2b08                _module_initcall_begin = .
                0x020c2b08                PROVIDE (module_initcall_begin, .)
 *(.module.initcall)
 .module.initcall
                0x020c2b08        0x8 ..\..\..\..\..\cpu\wl82\tools\sdk.elf.o
                0x020c2b08                __initcall_link_sw_iic0_data
                0x020c2b0c                __initcall_wait_completion_init
                0x020c2b10                _module_initcall_end = .
                0x020c2b10                PROVIDE (module_initcall_end, .)
                0x020c2b10                _sys_event_handler_begin = .
                0x020c2b10                PROVIDE (sys_event_handler_begin, .)
 *(.sys_event.*)
                0x020c2b10                _sys_event_handler_end = .
                0x020c2b10                PROVIDE (sys_event_handler_end, .)
                0x020c2b10                _syscfg_ops_begin = .
                0x020c2b10                PROVIDE (syscfg_ops_begin, .)
 *(.syscfg.2.ops)
 .syscfg.2.ops  0x020c2b10       0x1c ..\..\..\..\..\cpu\wl82\tools\sdk.elf.o
                0x020c2b10                cfg_btif
 *(.syscfg.1.ops)
 .syscfg.1.ops  0x020c2b2c       0x1c ..\..\..\..\..\cpu\wl82\tools\sdk.elf.o
                0x020c2b2c                cfg_vm
 *(.syscfg.0.ops)
 .syscfg.0.ops  0x020c2b48       0x1c ..\..\..\..\..\cpu\wl82\tools\sdk.elf.o
                0x020c2b48                cfg_bin
                0x020c2b64                _syscfg_ops_end = .
                0x020c2b64                PROVIDE (syscfg_ops_end, .)
                0x020c2b64                _server_info_begin = .
                [!provide]                PROVIDE (server_info_begin, .)
 *(.server_info)
                0x020c2b64                _server_info_end = .
                [!provide]                PROVIDE (server_info_end, .)
                0x020c2b64                _bus_device_begin = .
                [!provide]                PROVIDE (bus_device_begin, .)
 *(.bus_device)
                0x020c2b64                _bus_device_end = .
                [!provide]                PROVIDE (bus_device_end, .)
                0x020c2b64                _cmd_interface_begin = .
                [!provide]                PROVIDE (cmd_interface_begin, .)
 *(.eff_cmd)
                0x020c2b64                _cmd_interface_end = .
                [!provide]                PROVIDE (cmd_interface_end, .)
                0x020c2b64                _snap_interface_begin = .
                [!provide]                PROVIDE (snap_interface_begin, .)
 *(.snap_parm)
                0x020c2b64                _snap_interface_end = .
                [!provide]                PROVIDE (snap_interface_end, .)
                0x020c2b64                _device_node_begin = .
                0x020c2b64                PROVIDE (device_node_begin, .)
 *(.device)
 .device        0x020c2b64       0x30 ..\..\..\..\..\cpu\wl82\tools\sdk.elf.o
                0x020c2b64                device_table
                0x020c2b94                _device_node_end = .
                0x020c2b94                PROVIDE (device_node_end, .)
                0x020c2b94                lp_target_begin = .
                [!provide]                PROVIDE (lp_target_begin, .)
 *(.lp_target)
 .lp_target     0x020c2b94       0x58 ..\..\..\..\..\cpu\wl82\tools\sdk.elf.o
                0x020c2b94                iic_lp_target
                0x020c2b9c                adc_scan_lp_target
                0x020c2ba4                ota_lp_target
                0x020c2bac                uart_lp_target
                0x020c2bb4                spi_lp_target
                0x020c2bbc                sys_power_lp_target
                0x020c2bc4                dac_lp_target
                0x020c2bcc                norflash_lp_target
                0x020c2bd4                usb_dev_lp_target
                0x020c2bdc                usr_systimer_lp_target
                0x020c2be4                vm_lp_target
                0x020c2bec                lp_target_end = .
                [!provide]                PROVIDE (lp_target_end, .)
                0x020c2bec                _ai_sdk_api_begin = .
                [!provide]                PROVIDE (ai_sdk_api_begin, .)
 *(.ai_sdk)
                0x020c2bec                _ai_sdk_api_end = .
                [!provide]                PROVIDE (ai_sdk_api_end, .)
                0x020c2bec                config_target_begin = .
                [!provide]                PROVIDE (config_target_begin, .)
 *(.config_target)
                0x020c2bec                config_target_end = .
                [!provide]                PROVIDE (config_target_end, .)
                0x020c2bec                _tool_interface_begin = .
                [!provide]                PROVIDE (tool_interface_begin, .)
 *(.tool_interface)
                0x020c2bec                _tool_interface_end = .
                [!provide]                PROVIDE (tool_interface_end, .)
                0x020c2bec                _lcd_device_begin = .
                [!provide]                PROVIDE (lcd_device_begin, .)
 *(.lcd)
                0x020c2bec                _lcd_device_end = .
                [!provide]                PROVIDE (lcd_device_end, .)
                0x020c2bec                _camera_dev_begin = .
                [!provide]                PROVIDE (camera_dev_begin, .)
 *(.camera_dev)
                0x020c2bec                _camera_dev_end = .
                [!provide]                PROVIDE (camera_dev_end, .)
                0x020c2bec                _camera1_dev_begin = .
                [!provide]                PROVIDE (camera1_dev_begin, .)
 *(.camera1_dev)
                0x020c2bec                _camera1_dev_end = .
                [!provide]                PROVIDE (camera1_dev_end, .)
                0x020c2bec                _iic_device_begin = .
                0x020c2bec                PROVIDE (iic_device_begin, .)
 *(.iic)
 .iic           0x020c2bec       0x30 ..\..\..\..\..\cpu\wl82\tools\sdk.elf.o
                0x020c2c1c                _iic_device_end = .
                0x020c2c1c                PROVIDE (iic_device_end, .)
                0x020c2c1c                _video_pkg_sys_begin = .
                [!provide]                PROVIDE (video_pkg_sys_begin, .)
 *(.video_pkg_sys)
                0x020c2c1c                _video_pkg_sys_end = .
                [!provide]                PROVIDE (video_pkg_sys_end, .)
                0x020c2c1c                _video_pkg_begin = .
                [!provide]                PROVIDE (video_pkg_begin, .)
 *(.video_pkg)
                0x020c2c1c                _video_pkg_end = .
                [!provide]                PROVIDE (video_pkg_end, .)
                0x020c2c1c                . = ALIGN (0x4)
                0x020c2c1c                _audio_decoder_begin = .
                [!provide]                PROVIDE (audio_decoder_begin, .)
 *(.audio_decoder)
                0x020c2c1c                _audio_decoder_end = .
                [!provide]                PROVIDE (audio_decoder_end, .)
                0x020c2c1c                _audio_package_begin = .
                [!provide]                PROVIDE (audio_package_begin, .)
 *(.audio_package)
                0x020c2c1c                _audio_package_end = .
                [!provide]                PROVIDE (audio_package_end, .)
                0x020c2c1c                _audio_effect_begin = .
                [!provide]                PROVIDE (audio_effect_begin, .)
 *(.audio_effect)
                0x020c2c1c                _audio_effect_end = .
                [!provide]                PROVIDE (audio_effect_end, .)
                0x020c2c1c                _audio_hwaccel_begin = .
                [!provide]                PROVIDE (audio_hwaccel_begin, .)
 *(.audio_hwaccel)
                0x020c2c1c                _audio_hwaccel_end = .
                [!provide]                PROVIDE (audio_hwaccel_end, .)
                0x020c2c1c                _audio_output_handler_begin = .
                [!provide]                PROVIDE (audio_output_handler_begin, .)
 *(.audio_output_handler)
                0x020c2c1c                _audio_output_handler_end = .
                [!provide]                PROVIDE (audio_output_handler_end, .)
                0x020c2c1c                _audio_input_handler_begin = .
                [!provide]                PROVIDE (audio_input_handler_begin, .)
 *(.audio_input_handler)
                0x020c2c1c                _audio_input_handler_end = .
                [!provide]                PROVIDE (audio_input_handler_end, .)
                0x020c2c1c                _audio_subdev_begin = .
                [!provide]                PROVIDE (audio_subdev_begin, .)
 *(.audio_subdev.0)
 *(.audio_subdev.1)
 *(.audio_subdev.2)
 *(.audio_subdev.3)
                0x020c2c1c                _audio_subdev_end = .
                [!provide]                PROVIDE (audio_subdev_end, .)
                0x020c2c1c                _fm_dev_begin = .
                [!provide]                PROVIDE (fm_dev_begin, .)
 *(.fm_dev)
                0x020c2c1c                _fm_dev_end = .
                [!provide]                PROVIDE (fm_dev_end, .)
                0x020c2c1c                _video_dev_begin = .
                [!provide]                PROVIDE (video_dev_begin, .)
 *(.video_device)
                0x020c2c1c                _video_dev_end = .
                [!provide]                PROVIDE (video_dev_end, .)
                0x020c2c1c                _video_subdev_begin = .
                [!provide]                PROVIDE (video_subdev_begin, .)
 *(.video_subdev.0)
 *(.video_subdev.1)
 *(.video_subdev.2)
 *(.video_subdev.3)
 *(.video_subdev.4)
 *(.video_subdev.5)
                0x020c2c1c                _video_subdev_end = .
                [!provide]                PROVIDE (video_subdev_end, .)
                0x020c2c1c                . = ALIGN (0x4)
                0x020c2c1c                lcd_interface_begin = .
 *(.lcd_if_info)
                0x020c2c1c                lcd_interface_end = .
                0x020c2c1c                ui_style_begin = .
 *(.ui_style)
                0x020c2c1c                ui_style_end = .
                0x020c2c1c                elm_event_handler_begin_JL = .
 *(.elm_event_handler_JL)
                0x020c2c1c                elm_event_handler_end_JL = .
                0x020c2c1c                elm_event_handler_begin_UPGRADE = .
 *(.elm_event_handler_UPGRADE)
                0x020c2c1c                elm_event_handler_end_UPGRADE = .
                0x020c2c1c                elm_event_handler_begin_DIAL = .
 *(.elm_event_handler_DIAL)
                0x020c2c1c                elm_event_handler_end_DIAL = .
                0x020c2c1c                control_event_handler_begin = .
 *(.control_event_handler)
                0x020c2c1c                control_event_handler_end = .
                0x020c2c1c                control_ops_begin = .
 *(.control_ops)
                0x020c2c1c                control_ops_end = .
                0x020c2c1c                battery_notify_begin = .
 *(.battery_notify)
                0x020c2c1c                battery_notify_end = .
                0x020c2c20                . = ALIGN (0x20)
 *fill*         0x020c2c1c        0x4 
                0x020c2c20                _text_rodata_end = .
                [!provide]                PROVIDE (text_rodata_end, .)
                0x04000120                . = ORIGIN (sdram)

.data           0x04000120        0x0
                0x04000120                . = ALIGN (0x4)

.bss            0x04000120        0x0
                0x04000120                . = ALIGN (0x4)
                0x01c00000                . = ORIGIN (ram0)

.ram0_data      0x01c00000     0x388c
                0x01c00000                _ram_text_rodata_begin = .
                [!provide]                PROVIDE (ram_text_rodata_begin, .)
                0x01c00000                _VM_CODE_START = .
 *(.vm)
                0x01c00000                _VM_CODE_END = .
                0x01c00000                . = ALIGN (0x4)
                0x01c00000                _SPI_CODE_START = .
 *(.spi_code)
 .spi_code      0x01c00000      0xc44 ..\..\..\..\..\cpu\wl82\tools\sdk.elf.o
                0x01c00000                sfc_drop_cache
                0x01c00564                flash_addr2cpu_addr
                0x01c00690                cpu_addr2flash_addr
                0x01c00848                norflash_enter_spi_code
                0x01c00888                norflash_erase
                0x01c00922                norflash_eraser_otp
                0x01c009e8                norflash_exit_spi_code
                0x01c00a20                norflash_read_otp
                0x01c00a84                norflash_read_otp_general
                0x01c00b06                norflash_spi_cs
                0x01c00b10                norflash_spi_read_byte
                0x01c00b18                norflash_spi_write_byte
                0x01c00b36                norflash_wait_busy
                0x01c00b3e                norflash_write_otp
                0x01c00bac                read_flash_id
                0x01c00c36                sfc_protect
                0x01c00c40                sfc_resume
                0x01c00c42                sfc_suspend
                0x01c00c44                . = ALIGN (0x4)
                0x01c00c44                _SPI_CODE_END = .
 *(.flushinv_icache)
 .flushinv_icache
                0x01c00c44      0x21c ..\..\..\..\..\cpu\wl82\tools\sdk.elf.o
                0x01c00d1e                icache_flush
                0x01c00d50                cache_way_config
 .flushinv_icache
                0x01c00e60       0x5e ../../../../../cpu/wl82/liba/system.a(movable.S.o)
                0x01c00e60                movable_swi_entry
 *(.volatile_ram_code)
 .volatile_ram_code
                0x01c00ebe     0x152e ..\..\..\..\..\cpu\wl82\tools\sdk.elf.o
                0x01c00ebe                cpu_irq_disabled
                0x01c00ee2                __local_irq_disable
                0x01c00efa                __local_irq_enable
 *(*.text.cache.L1)
 .movable.text.cache.L1
                0x01c023ec       0xcc ..\..\..\..\..\cpu\wl82\tools\sdk.elf.o
 *(*.text.cache.L2)
                0x01c024b8                . = ALIGN (0x4)
                0x01c024b8                __rf_trim_movable_slot_start = .
 *(.movable.slot.1)
 .movable.slot.1
                0x01c024b8       0x84 ..\..\..\..\..\cpu\wl82\tools\sdk.elf.o
                0x01c024b8                bbp_set.slot
                0x01c024bc                sdio_mac_wreg.slot
                0x01c024c0                sdio_mac_rreg.slot
                0x01c024c4                cca_reg_set.slot
                0x01c024c8                tx_backend_wr.slot
                0x01c024cc                tx_backend_rd.slot
                0x01c024d0                rx_frontend_wr.slot
                0x01c024d4                rx_frontend_rd.slot
                0x01c024d8                wf_phy_rst.slot
                0x01c024dc                wlrf_get_.slot
                0x01c024e0                wf_rf_en_set.slot
                0x01c024e4                wfrf_flt_vga_gain.slot
                0x01c024e8                wfrf_hp_open.slot
                0x01c024ec                wfrf_hp_close.slot
                0x01c024f0                rx_tx_cal_attenuation.slot
                0x01c024f4                wf_rx_tune_test.slot
                0x01c024f8                txlo_cali_set.slot
                0x01c024fc                txlo_calq_set.slot
                0x01c02500                set_anl_rxdc_q.slot
                0x01c02504                set_anl_rxdc_i.slot
                0x01c02508                set_dig_tx_i_dc.slot
                0x01c0250c                set_dig_tx_q_dc.slot
                0x01c02510                set_dig_tx_i_gain.slot
                0x01c02514                set_dig_tx_q_gain.slot
                0x01c02518                set_dig_tx_i_phase.slot
                0x01c0251c                set_dig_tx_q_phase.slot
                0x01c02520                set_dig_rx_i_gain.slot
                0x01c02524                set_dig_rx_q_gain.slot
                0x01c02528                set_dig_rx_i_phase.slot
                0x01c0252c                set_dig_rx_q_phase.slot
                0x01c02530                txiq_tune_soft.slot
                0x01c02534                rxdc_tune_soft.slot
                0x01c02538                rxiq_tune_soft.slot
                0x01c0253c                __rf_trim_movable_slot_end = .
                0x01c0253c                . = ALIGN (0x4)
                0x01c0253c                _ram_text_rodata_end = .
                [!provide]                PROVIDE (ram_text_rodata_end, .)
                0x01c0253c                . = ALIGN (0x4)
 *(.data)
 *fill*         0x01c0253c        0x4 
 .data          0x01c02540      0xc18 ..\..\..\..\..\cpu\wl82\tools\sdk.elf.o
 .data          0x01c03158        0x4 ../../../../../include_lib/newlib/pi32v2-lib/libc.a(lib_a-ctype_.o)
                0x01c03158                __ctype_ptr__
 .data          0x01c0315c      0x428 ../../../../../include_lib/newlib/pi32v2-lib/libc.a(lib_a-impure.o)
                0x01c0315c                _impure_ptr
 .data          0x01c03584       0x7c ../../../../../include_lib/newlib/pi32v2-lib/libc.a(lib_a-locale.o)
                0x01c03584                __mb_cur_max
                0x01c03600                . = ALIGN (0x4)
 *(.volatile_ram)
 .volatile_ram  0x01c03600      0x23c ..\..\..\..\..\cpu\wl82\tools\sdk.elf.o
                0x01c0382c                cpu_lock_cnt
                0x01c03834                irq_lock_cnt
 *(.non_volatile_ram)
 *(.fft_data)
 *(.deepsleep_target)
 *(.os_data)
 .os_data       0x01c0383c       0x4f ..\..\..\..\..\cpu\wl82\tools\sdk.elf.o
                0x01c03844                CurrentTCB
                0x01c03858                highCurrentTCB
 *(.os_port_data)
                0x01c0388c                . = ALIGN (0x4)
 *fill*         0x01c0388b        0x1 
                0x01c0388c                _ctp_mapping_tab_begin = .
                [!provide]                PROVIDE (ctp_mapping_tab_begin, .)
                0x01c0388c                _ctp_mapping_tab_system_cmd_begin = .
                [!provide]                PROVIDE (ctp_mapping_tab_system_cmd_begin, .)
 *(.ctp_system_cmd)
                0x01c0388c                _ctp_mapping_tab_system_cmd_end = .
                [!provide]                PROVIDE (ctp_mapping_tab_system_cmd_end, .)
                0x01c0388c                _ctp_mapping_tab_video_cmd_begin = .
                [!provide]                PROVIDE (ctp_mapping_tab_video_cmd_begin, .)
 *(.ctp_video_cmd)
                0x01c0388c                _ctp_mapping_tab_video_cmd_end = .
                [!provide]                PROVIDE (ctp_mapping_tab_video_cmd_end, .)
                0x01c0388c                _ctp_mapping_tab_photo_cmd_begin = .
                [!provide]                PROVIDE (ctp_mapping_tab_photo_cmd_begin, .)
 *(.ctp_photo_cmd)
                0x01c0388c                _ctp_mapping_tab_photo_cmd_end = .
                [!provide]                PROVIDE (ctp_mapping_tab_photo_cmd_end, .)
                0x01c0388c                _ctp_mapping_tab_end = .
                [!provide]                PROVIDE (ctp_mapping_tab_end, .)
                0x01c0388c                _net_video_stream_sub_begin = .
                [!provide]                PROVIDE (net_video_stream_sub_begin, .)
 *(.net_video_stream)
                0x01c0388c                _net_video_stream_sub_end = .
                [!provide]                PROVIDE (net_video_stream_sub_end, .)
                0x01c0388c                . = ALIGN (0x4)

.ram0_bss       0x01c0388c    0x24674
 *(.bss)
 *fill*         0x01c0388c        0x4 
 .bss           0x01c03890    0x20c70 ..\..\..\..\..\cpu\wl82\tools\sdk.elf.o
 .bss           0x01c24500        0xc ../../../../../include_lib/newlib/pi32v2-lib/libc.a(lib_a-locale.o)
                0x01c24500                __nlocale_changed
                0x01c24504                __mlocale_changed
                0x01c24508                _PathLocale
 *(COMMON)
 *(.mem_heap)
                0x01c2450c                . = ALIGN (0x4)
 *(.os_port_bss)
 *(.os_bss)
 .os_bss        0x01c2450c      0x908 ..\..\..\..\..\cpu\wl82\tools\sdk.elf.o
 *(.update_bss)
                0x01c24e14                . = ALIGN (0x4)
 *(.the_debug_isr_stack_c0)
 *(.the_debug_isr_stack_c1)
 *(.stack_magic)
 *(.stack_magic0)
                0x01c24e14                . = ALIGN (0x4)
                0x01c24e14                _stack_info_begin = .
                [!provide]                PROVIDE (stack_info_begin, .)
 *(.cpu0_ustack)
 .cpu0_ustack   0x01c24e14      0x300 ../../../../../cpu/wl82/liba/cpu.a(startup.S.o)
                0x01c25114                _cpu0_ustack
                0x01c25114                _cpu0_sstack_begin = .
                0x01c25114                PROVIDE (cpu0_sstack_begin, .)
 *(.cpu0_sstack)
 .cpu0_sstack   0x01c25114     0x1000 ../../../../../cpu/wl82/liba/cpu.a(startup.S.o)
                0x01c26114                _cpu0_sstack
                0x01c26114                _cpu0_sstack_end = .
                0x01c26114                PROVIDE (cpu0_sstack_end, .)
 *(.cpu1_ustack)
 .cpu1_ustack   0x01c26114      0x300 ../../../../../cpu/wl82/liba/cpu.a(startup.S.o)
                0x01c26414                _cpu1_ustack
                0x01c26414                _cpu1_sstack_begin = .
                0x01c26414                PROVIDE (cpu1_sstack_begin, .)
 *(.cpu1_sstack)
 .cpu1_sstack   0x01c26414     0x1000 ../../../../../cpu/wl82/liba/cpu.a(startup.S.o)
                0x01c27414                _cpu1_sstack
                0x01c27414                _cpu1_sstack_end = .
                0x01c27414                PROVIDE (cpu1_sstack_end, .)
                0x01c27414                _stack_info_end = .
                [!provide]                PROVIDE (stack_info_end, .)
 *(.sram)
 *fill*         0x01c27414        0xc 
 .sram          0x01c27420      0xae0 ..\..\..\..\..\cpu\wl82\tools\sdk.elf.o
                0x01c274f1                cpu1_run_flag
                0x01c27f00                . = ALIGN (0x4)
                0x01c27f00                _usb_data_begin = .
                [!provide]                PROVIDE (usb_data_begin, .)
 *(.usb_h_bss)
 *(.usb_g_bss)
 *(.usb_h_dma)
 *(.usb_config_var)
 *(.usb_msd_dma)
 *(.usb_fifo)
 *(.mass_storage)
 *(.uac_var)
 *(.uac_rx)
 *(.usb_h_udisk)
 *(.cdc_var)
                0x01c27f00                _usb_data_end = .
                [!provide]                PROVIDE (usb_data_end, .)
                0x01c27f00                . = ALIGN (0x4)
                0x01c27f00                _HEAP_BEGIN = (((. + 0x1f) / 0x20) * 0x20)
                0x01c27f00                PROVIDE (HEAP_BEGIN, (((. + 0x1f) / 0x20) * 0x20))
                0x01c7fd2c                _HEAP_END = ((0x1c00000 + RAM0_SIZE) - 0x20)
                0x01c7fd2c                PROVIDE (HEAP_END, ((0x1c00000 + RAM0_SIZE) - 0x20))
                0x00057e2c                _MALLOC_SIZE = (_HEAP_END - _HEAP_BEGIN)
                [!provide]                PROVIDE (MALLOC_SIZE, (_HEAP_END - _HEAP_BEGIN))
                [!provide]                PROVIDE (RAM_HEAP_BEGIN, 0x0)
                [!provide]                PROVIDE (RAM_HEAP_END, 0x0)
                [!provide]                PROVIDE (RAM_MALLOC_SIZE, 0x0)
                0x01c7fd4c                . = ORIGIN (boot_info)

.boot_info      0x01c7fd4c       0x34
 *(.boot_info)
 .boot_info     0x01c7fd4c       0x34 ..\..\..\..\..\cpu\wl82\tools\sdk.elf.o
                0x01c7fd80                . = ALIGN (0x4)
                0x01f28000                . = ORIGIN (cache_ram)

.cache_ram_data
                0x01f28000       0x40
                0x01f28000                _app_begin = .
                0x01f28000                PROVIDE (app_begin, .)
 *(.app)
 .app           0x01f28000       0x20 ..\..\..\..\..\cpu\wl82\tools\sdk.elf.o
                0x01f28020                _app_end = .
                0x01f28020                PROVIDE (app_end, .)
                0x01f28020                _static_hi_timer_begin = .
                [!provide]                PROVIDE (static_hi_timer_begin, .)
 *(.hi_timer)
                0x01f28020                _static_hi_timer_end = .
                [!provide]                PROVIDE (static_hi_timer_end, .)
                0x01f28020                _sys_cpu_timer_begin = .
                0x01f28020                PROVIDE (sys_cpu_timer_begin, .)
 *(.sys_cpu_timer)
 .sys_cpu_timer
                0x01f28020       0x10 ..\..\..\..\..\cpu\wl82\tools\sdk.elf.o
                0x01f28020                cpu_timer4
                0x01f28030                _sys_cpu_timer_end = .
                0x01f28030                PROVIDE (sys_cpu_timer_end, .)
                0x01f28030                _gsensor_dev_begin = .
                [!provide]                PROVIDE (gsensor_dev_begin, .)
 *(.gsensor_dev)
                0x01f28030                _gsensor_dev_end = .
                [!provide]                PROVIDE (gsensor_dev_end, .)
                0x01f28030                _clock_critical_handler_begin = .
                0x01f28030                PROVIDE (clock_critical_handler_begin, .)
 *(.clock_critical_txt)
 .clock_critical_txt
                0x01f28030       0x10 ..\..\..\..\..\cpu\wl82\tools\sdk.elf.o
                0x01f28030                clock_timer1
                0x01f28038                clock_port
                0x01f28040                _clock_critical_handler_end = .
                0x01f28040                PROVIDE (clock_critical_handler_end, .)
                0x01f28040                . = ALIGN (0x4)

.cache_ram_bss  0x01f28040     0x5da8
 *(.memp_memory_x)
 .memp_memory_x
                0x01f28040     0x5da7 ..\..\..\..\..\cpu\wl82\tools\sdk.elf.o
                0x01f2dde8                . = ALIGN (0x4)
 *fill*         0x01f2dde7        0x1 
                0x02000120                text_begin = ADDR (.text)
                0x000c2b00                text_size = SIZEOF (.text)
                [!provide]                PROVIDE (text_size, SIZEOF (.text))
                0x04000120                bss_begin = ADDR (.bss)
                0x00000000                bss_size = SIZEOF (.bss)
                [!provide]                PROVIDE (bss_size, SIZEOF (.bss))
                0x04000120                data_vma = ADDR (.data)
                0x020c2c20                data_lma = (text_begin + SIZEOF (.text))
                0x00000000                data_size = SIZEOF (.data)
                [!provide]                PROVIDE (data_size, SIZEOF (.data))
                0x01c0388c                _ram0_bss_vma = ADDR (.ram0_bss)
                0x00024674                _ram0_bss_size = SIZEOF (.ram0_bss)
                0x00024674                PROVIDE (ram0_bss_size, SIZEOF (.ram0_bss))
                0x01c00000                _ram0_data_vma = ADDR (.ram0_data)
                0x020c2c20                _ram0_data_lma = ((text_begin + SIZEOF (.text)) + SIZEOF (.data))
                0x0000388c                _ram0_data_size = SIZEOF (.ram0_data)
                0x0000388c                PROVIDE (ram0_data_size, SIZEOF (.ram0_data))
                0x01f28040                PROVIDE (cache_ram_bss_vma, ADDR (.cache_ram_bss))
                0x00005da8                PROVIDE (cache_ram_bss_size, SIZEOF (.cache_ram_bss))
                0x01f28000                PROVIDE (cache_ram_data_vma, ADDR (.cache_ram_data))
                0x020c64ac                PROVIDE (cache_ram_data_lma, (((text_begin + SIZEOF (.text)) + SIZEOF (.data)) + SIZEOF (.ram0_data)))
                0x00000040                PROVIDE (cache_ram_data_size, SIZEOF (.cache_ram_data))
END GROUP
OUTPUT(..\..\..\..\..\cpu\wl82\tools\sdk.elf elf32-pi32v2)

.debug_str      0x00000000    0x4d3d5
 .debug_str     0x00000000    0x4d3d5 ..\..\..\..\..\cpu\wl82\tools\sdk.elf.o
                              0x53f95 (size before relaxing)

.debug_loc      0x00000000    0x72892
 .debug_loc     0x00000000    0x72892 ..\..\..\..\..\cpu\wl82\tools\sdk.elf.o

.debug_abbrev   0x00000000     0x102d
 .debug_abbrev  0x00000000      0xfb5 ..\..\..\..\..\cpu\wl82\tools\sdk.elf.o
 .debug_abbrev  0x00000fb5       0x28 ../../../../../cpu/wl82/liba/cpu.a(startup.S.o)
 .debug_abbrev  0x00000fdd       0x28 ../../../../../cpu/wl82/liba/system.a(port_asm.S.o)
 .debug_abbrev  0x00001005       0x28 ../../../../../cpu/wl82/liba/system.a(movable.S.o)

.debug_info     0x00000000   0x24a667
 .debug_info    0x00000000   0x24a2ae ..\..\..\..\..\cpu\wl82\tools\sdk.elf.o
 .debug_info    0x0024a2ae      0x1ce ../../../../../cpu/wl82/liba/cpu.a(startup.S.o)
 .debug_info    0x0024a47c       0xfd ../../../../../cpu/wl82/liba/system.a(port_asm.S.o)
 .debug_info    0x0024a579       0xee ../../../../../cpu/wl82/liba/system.a(movable.S.o)

.debug_ranges   0x00000000     0xca70
 .debug_ranges  0x00000000     0xca70 ..\..\..\..\..\cpu\wl82\tools\sdk.elf.o

.debug_macinfo  0x00000000        0x1
 .debug_macinfo
                0x00000000        0x1 ..\..\..\..\..\cpu\wl82\tools\sdk.elf.o

.debug_pubnames
                0x00000000    0x2685f
 .debug_pubnames
                0x00000000    0x2685f ..\..\..\..\..\cpu\wl82\tools\sdk.elf.o

.debug_pubtypes
                0x00000000    0x4aa36
 .debug_pubtypes
                0x00000000    0x4aa36 ..\..\..\..\..\cpu\wl82\tools\sdk.elf.o

.debug_frame    0x00000000    0x15778
 .debug_frame   0x00000000    0x15778 ..\..\..\..\..\cpu\wl82\tools\sdk.elf.o

.debug_line     0x00000000    0xb2f29
 .debug_line    0x00000000    0xb2811 ..\..\..\..\..\cpu\wl82\tools\sdk.elf.o
 .debug_line    0x000b2811       0x2c obj/Release/include_lib/btctrler/version.z.o
 .debug_line    0x000b283d       0x2c obj/Release/include_lib/btstack/version.z.o
 .debug_line    0x000b2869       0x2c obj/Release/include_lib/driver/version.z.o
 .debug_line    0x000b2895       0x2c obj/Release/include_lib/media/version.z.o
 .debug_line    0x000b28c1       0x2c obj/Release/include_lib/net/version.z.o
 .debug_line    0x000b28ed       0x2c obj/Release/include_lib/server/version.z.o
 .debug_line    0x000b2919       0x2c obj/Release/include_lib/system/version.z.o
 .debug_line    0x000b2945       0x2c obj/Release/include_lib/update/version.z.o
 .debug_line    0x000b2971       0x2c obj/Release/include_lib/utils/version.z.o
 .debug_line    0x000b299d       0x2c obj/Release/lib/net/version.z.o
 .debug_line    0x000b29c9       0x2c obj/Release/lib/server/version.z.o
 .debug_line    0x000b29f5       0x2c obj/Release/lib/utils/version.z.o
 .debug_line    0x000b2a21       0xc7 ../../../../../cpu/wl82/liba/cpu.a(startup.S.o)
 .debug_line    0x000b2ae8       0x2c ../../../../../cpu/wl82/liba/cpu.a(version.z.o)
 .debug_line    0x000b2b14       0x2c ../../../../../cpu/wl82/liba/fs.a(version.z.o)
 .debug_line    0x000b2b40       0x2c ../../../../../cpu/wl82/liba/event.a(version.z.o)
 .debug_line    0x000b2b6c       0xca ../../../../../cpu/wl82/liba/system.a(port_asm.S.o)
 .debug_line    0x000b2c36       0xb7 ../../../../../cpu/wl82/liba/system.a(movable.S.o)
 .debug_line    0x000b2ced       0x2c ../../../../../cpu/wl82/liba/system.a(version.z.o)
 .debug_line    0x000b2d19       0x2c ../../../../../cpu/wl82/liba/cfg_tool.a(version.z.o)
 .debug_line    0x000b2d45       0x2c ../../../../../cpu/wl82/liba/common_lib.a(version.z.o)
 .debug_line    0x000b2d71       0x2c ../../../../../cpu/wl82/liba/update.a(version.z.o)
 .debug_line    0x000b2d9d       0x2c ../../../../../cpu/wl82/liba/wl_rf_common.a(version.z.o)
 .debug_line    0x000b2dc9       0x2c ../../../../../cpu/wl82/liba/hsm.a(version.z.o)
 .debug_line    0x000b2df5       0x2c ../../../../../cpu/wl82/liba/wpasupplicant.a(version.z.o)
 .debug_line    0x000b2e21       0x2c ../../../../../cpu/wl82/liba/http_cli.a(version.z.o)
 .debug_line    0x000b2e4d       0x2c ../../../../../cpu/wl82/liba/json.a(version.z.o)
 .debug_line    0x000b2e79       0x2c ../../../../../cpu/wl82/liba/iperf.a(version.z.o)
 .debug_line    0x000b2ea5       0x2c ../../../../../cpu/wl82/liba/libmbedtls_2_2_1.a(version.z.o)
 .debug_line    0x000b2ed1       0x2c ../../../../../cpu/wl82/liba/lwip_2_1_3_sfc.a(version.z.o)
 .debug_line    0x000b2efd       0x2c ../../../../../cpu/wl82/liba/wl_wifi_sfc.a(version.z.o)

.debug_aranges  0x00000000       0x60
 .debug_aranges
                0x00000000       0x20 ../../../../../cpu/wl82/liba/cpu.a(startup.S.o)
 .debug_aranges
                0x00000020       0x20 ../../../../../cpu/wl82/liba/system.a(port_asm.S.o)
 .debug_aranges
                0x00000040       0x20 ../../../../../cpu/wl82/liba/system.a(movable.S.o)
